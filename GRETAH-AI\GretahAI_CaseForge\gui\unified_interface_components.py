"""
<PERSON><PERSON>h<PERSON><PERSON>_CaseForge - Unified Interface UI Components

Professional UI components for the unified test case interface with comprehensive
filtering capabilities, consistent styling, and optimal user experience.

Author: GretahAI Development Team
Date: 2025-01-14
Version: 1.0.0
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# Import database functions
from db_helper import (
    get_unified_filtered_test_cases,
    get_filter_options,
    DATABASE_PATH
)

# Import existing UI helpers
from helpers.ui import create_jira_details_css
from helpers.data import count_valid_test_cases

# Import existing export helpers
from helpers.csv.export import export_test_cases_to_csv, format_csv_for_external_tools
from helpers.excel.formatting import create_formatted_excel_from_scenarios

# Import configuration
from .unified_interface_config import (
    DEFAULT_FILTERS, UI_CONFIG, EDITABLE_COLUMNS, COLUMN_ORDER,
    EXPORT_CONFIG, SESSION_KEYS, HELP_TEXT, ERROR_MESSAGES,
    get_default_date_range, get_session_key, get_export_filename,
    get_mime_type, get_filter_help_text, get_error_message,
    get_ui_filter_key, get_applied_filter_key, get_smart_default_key
)

def create_unified_interface_css():
    """
    Create custom CSS for the unified interface with professional styling.
    
    Returns:
        str: CSS style definitions for unified interface
    """
    return """
    <style>
    /* Unified Interface Styling */
    
    /* Removed filter-container styling for cleaner HTML structure */
    
    /* Removed filter-section-header styling - using native Streamlit subheader */
    
    /* Removed count-indicator styling for cleaner UI */
    
    .filter-group {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
    }
    
    .filter-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    /* Removed export-section styling for cleaner HTML structure */
    
    /* Removed export-header styling - using native Streamlit subheader */
    
    /* Removed data-table-container styling for cleaner HTML structure */
    
    /* Removed table-header styling - using native Streamlit subheader */
    
    /* Button styling */
    .stButton > button {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    /* Selectbox and input styling */
    .stSelectbox > div > div {
        border-radius: 6px;
        border: 1px solid #ced4da;
    }
    
    .stDateInput > div > div {
        border-radius: 6px;
        border: 1px solid #ced4da;
    }
    
    /* Expander styling */
    .streamlit-expanderHeader {
        background: #f8f9fa;
        border-radius: 6px;
        font-weight: 600;
    }
    
    /* Alert styling */
    .stAlert {
        border-radius: 6px;
    }
    
    /* Success message styling */
    .stSuccess {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 1px solid #c3e6cb;
        border-radius: 6px;
    }
    
    /* Warning message styling */
    .stWarning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 1px solid #ffeaa7;
        border-radius: 6px;
    }
    
    /* Info message styling */
    .stInfo {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        border: 1px solid #bee5eb;
        border-radius: 6px;
    }
    </style>
    """

def has_valid_test_case_id_range_data() -> bool:
    """
    Check if we have valid Test Case ID range data that should be maintained.

    Returns:
        bool: True if valid Test Case ID range data exists
    """
    try:
        # Check if we have generation tracking data
        generation_start = st.session_state.get("generation_start_id")
        generation_end = st.session_state.get("generation_end_id")

        if generation_start is not None and generation_end is not None:
            print(f"🔍 Found generation tracking data: {generation_start} to {generation_end}")
            return True

        # Check if Test Case ID range is already set in session state
        start_key = get_session_key('filters', 'test_case_id_range_start')
        end_key = get_session_key('filters', 'test_case_id_range_end')

        start_value = st.session_state.get(start_key)
        end_value = st.session_state.get(end_key)

        if start_value is not None and end_value is not None:
            print(f"🔍 Found existing Test Case ID range filters: {start_value} to {end_value}")
            return True

        # Check if we can detect range from database
        range_info = get_current_generation_test_case_id_range()
        if range_info['range_available']:
            print(f"🔍 Found database-detected range: {range_info['start_id']} to {range_info['end_id']}")
            return True

        print("🔍 No valid Test Case ID range data found")
        return False

    except Exception as e:
        print(f"⚠️ Error checking Test Case ID range data: {e}")
        return False

def store_smart_defaults_from_generation():
    """
    Store smart default values from the current generation context.
    This should be called after test case generation to capture the generation context.
    """
    try:
        # Store smart defaults based on generation context
        smart_defaults = {
            'test_type': st.session_state.get("test_type_select", "all"),
            'ai_generated': 'Yes',  # Generated test cases are AI generated
            'jira_id': st.session_state.get("jira_case_id_input", "All"),
            'enhanced_status': 'Yes' if st.session_state.get("using_enhanced_for_generation", False) else 'All'
        }

        # Store Test Case ID range if available
        range_info = get_current_generation_test_case_id_range()
        if range_info['range_available']:
            smart_defaults['test_case_id_range_start'] = range_info['start_id']
            smart_defaults['test_case_id_range_end'] = range_info['end_id']

            # Set date range to today for additional context
            today = datetime.now().date()
            smart_defaults['date_start'] = today
            smart_defaults['date_end'] = today
        else:
            # Fallback to today's date
            today = datetime.now().date()
            smart_defaults['date_start'] = today
            smart_defaults['date_end'] = today

        # Store smart defaults in session state
        for key, value in smart_defaults.items():
            smart_default_key = get_smart_default_key(key)
            st.session_state[smart_default_key] = value
            print(f"🎯 Stored smart default: {smart_default_key} = {value}")

        print("✅ Smart defaults stored from generation context")

    except Exception as e:
        print(f"⚠️ Error storing smart defaults: {e}")

def apply_filters_to_database():
    """
    Apply current UI filter state to applied filter state and trigger database query.
    This function transfers UI filter values to the applied filter state used for database queries.
    """
    try:
        print("🔄 Applying UI filters to database query state...")

        # Transfer UI filter state to applied filter state - Enhanced with new filters
        ui_to_applied_mapping = {
            'test_type': 'test_type',
            'feature': 'feature',
            'project': 'project',
            'priority': 'priority',
            'test_group': 'test_group',
            'user_name': 'user_name',
            'jira_id': 'jira_id',
            'date_start': 'date_start',
            'date_end': 'date_end',
            'ai_generated': 'ai_generated',
            'enhanced_status': 'enhanced_status',
            'is_edited': 'is_edited',
            'test_case_id_range_start': 'test_case_id_range_start',
            'test_case_id_range_end': 'test_case_id_range_end'
        }

        for ui_key, applied_key in ui_to_applied_mapping.items():
            ui_session_key = get_ui_filter_key(ui_key)
            applied_session_key = get_applied_filter_key(applied_key)

            if ui_session_key in st.session_state:
                ui_value = st.session_state[ui_session_key]
                st.session_state[applied_session_key] = ui_value
                print(f"📊 Applied filter: {applied_key} = {ui_value}")

        # Clear export state when filters are applied
        clear_export_state()

        # Clear AI-modified data when filters change (AI modifications might not match new filters)
        if "ai_modified_data" in st.session_state:
            del st.session_state["ai_modified_data"]
        if "ai_modification_timestamp" in st.session_state:
            del st.session_state["ai_modification_timestamp"]
        print("🧹 Cleared AI modifications due to filter change")

        print("✅ Filters applied to database query state")

    except Exception as e:
        print(f"⚠️ Error applying filters: {e}")

def reset_filters_to_smart_defaults():
    """
    Reset all filters to their smart default values from generation.
    This function restores the original smart defaults and applies them immediately.
    """
    try:
        print("🔄 Resetting filters to smart defaults...")

        # Get stored smart defaults
        smart_default_keys = ['test_type', 'date_start', 'date_end', 'ai_generated',
                             'enhanced_status', 'jira_id', 'test_case_id_range_start',
                             'test_case_id_range_end']

        for key in smart_default_keys:
            smart_default_key = get_smart_default_key(key)
            ui_filter_key = get_ui_filter_key(key)
            applied_filter_key = get_applied_filter_key(key)

            if smart_default_key in st.session_state:
                smart_value = st.session_state[smart_default_key]
                # Set both UI and applied state to smart default
                st.session_state[ui_filter_key] = smart_value
                st.session_state[applied_filter_key] = smart_value
                print(f"🎯 Reset to smart default: {key} = {smart_value}")
            else:
                # Fallback to regular default if smart default not available
                default_value = DEFAULT_FILTERS.get(key)
                if default_value is not None:
                    st.session_state[ui_filter_key] = default_value
                    st.session_state[applied_filter_key] = default_value
                    print(f"🔧 Reset to regular default: {key} = {default_value}")

        # Clear export state when filters are reset
        clear_export_state()

        print("✅ Filters reset to smart defaults")

    except Exception as e:
        print(f"⚠️ Error resetting filters: {e}")

def initialize_unified_session_state(apply_smart_defaults=False):
    """
    Initialize session state variables for unified interface.

    Args:
        apply_smart_defaults (bool): If True, apply smart defaults for newly generated test cases

    Sets up default filter values and ensures all necessary session state
    variables are properly initialized for the unified interface.
    """
    # Check if this is the first time showing the interface after generation
    first_time_after_generation = (
        apply_smart_defaults and
        st.session_state.get("test_cases_generated", False) and
        not st.session_state.get("unified_interface_initialized", False)
    )

    print(f"🔍 Session state analysis:")
    print(f"   first_time_after_generation: {first_time_after_generation}")
    print(f"   test_cases_generated: {st.session_state.get('test_cases_generated', False)}")
    print(f"   unified_interface_initialized: {st.session_state.get('unified_interface_initialized', False)}")

    # Store smart defaults from generation context if this is the first time
    if first_time_after_generation:
        store_smart_defaults_from_generation()

    # Initialize UI filter state and applied filter state
    filter_keys = ['test_type', 'feature', 'project', 'priority', 'test_group', 'user_name', 'jira_id',
                   'date_start', 'date_end', 'ai_generated', 'enhanced_status',
                   'is_edited', 'test_case_id_range_start', 'test_case_id_range_end']

    for key in filter_keys:
        ui_filter_key = get_ui_filter_key(key)
        applied_filter_key = get_applied_filter_key(key)
        smart_default_key = get_smart_default_key(key)

        # Initialize UI filter state
        if ui_filter_key not in st.session_state:
            if first_time_after_generation and smart_default_key in st.session_state:
                # Use smart default for UI state
                st.session_state[ui_filter_key] = st.session_state[smart_default_key]
                print(f"🎯 Set UI filter from smart default: {key} = {st.session_state[smart_default_key]}")
            else:
                # Use regular default for UI state
                if key == 'date_start':
                    start_date, _ = get_default_date_range()
                    st.session_state[ui_filter_key] = start_date
                elif key == 'date_end':
                    _, end_date = get_default_date_range()
                    st.session_state[ui_filter_key] = end_date
                else:
                    default_value = DEFAULT_FILTERS.get(key)
                    st.session_state[ui_filter_key] = default_value
                    print(f"🔧 Set UI filter to default: {key} = {default_value}")

        # Initialize applied filter state
        if applied_filter_key not in st.session_state:
            if first_time_after_generation and smart_default_key in st.session_state:
                # Use smart default for applied state (auto-apply on first time)
                st.session_state[applied_filter_key] = st.session_state[smart_default_key]
                print(f"🎯 Set applied filter from smart default: {key} = {st.session_state[smart_default_key]}")
            else:
                # Use regular default for applied state
                if key == 'date_start':
                    start_date, _ = get_default_date_range()
                    st.session_state[applied_filter_key] = start_date
                elif key == 'date_end':
                    _, end_date = get_default_date_range()
                    st.session_state[applied_filter_key] = end_date
                else:
                    default_value = DEFAULT_FILTERS.get(key)
                    st.session_state[applied_filter_key] = default_value
                    print(f"🔧 Set applied filter to default: {key} = {default_value}")

    # Initialize export state using configuration
    for key in SESSION_KEYS['export']:
        session_key = get_session_key('export', key)
        if session_key not in st.session_state:
            if key == 'ready':
                st.session_state[session_key] = False
            else:
                st.session_state[session_key] = None

    # Initialize cache state
    for key in SESSION_KEYS['cache']:
        session_key = get_session_key('cache', key)
        if session_key not in st.session_state:
            if key == 'filter_options':
                st.session_state[session_key] = {}
            else:
                st.session_state[session_key] = None

    # Mark interface as initialized if applying smart defaults
    if first_time_after_generation:
        st.session_state["unified_interface_initialized"] = True

def ensure_test_case_id_range_filtering_applied():
    """
    Ensure that Test Case ID range filtering is applied to session state.
    This function maintains persistent filtering across UI interactions.
    """
    try:
        start_key = get_session_key('filters', 'test_case_id_range_start')
        end_key = get_session_key('filters', 'test_case_id_range_end')

        # Check if Test Case ID range filters are already set
        start_value = st.session_state.get(start_key)
        end_value = st.session_state.get(end_key)

        if start_value is not None and end_value is not None:
            print(f"🔒 Test Case ID range filtering already applied: TC_{start_value:03d} to TC_{end_value:03d}")
            return

        # Try to get range info and apply it
        range_info = get_current_generation_test_case_id_range()

        if range_info['range_available']:
            st.session_state[start_key] = range_info['start_id']
            st.session_state[end_key] = range_info['end_id']

            print(f"🎯 Applied persistent Test Case ID range filtering: TC_{range_info['start_id']:03d} to TC_{range_info['end_id']:03d}")
            print(f"🔄 Strategy: {range_info['strategy']} (persistent across interactions)")
        else:
            print("ℹ️ No Test Case ID range data available for persistent filtering")

    except Exception as e:
        print(f"⚠️ Error ensuring Test Case ID range filtering: {e}")

def track_test_case_id_before_generation():
    """
    Track the highest test case ID before generation starts.
    This should be called before test case generation begins.
    """
    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return

        # Get the highest test case ID from database
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH

        highest_id = get_highest_test_case_id_number(DATABASE_PATH, jira_id, "all")

        # Store the last known test case ID before generation
        st.session_state[get_session_key('generation_tracking', 'last_known_test_case_id')] = highest_id
        st.session_state[get_session_key('generation_tracking', 'generation_start_id')] = highest_id + 1

        print(f"Tracked test case ID before generation: Last known ID = {highest_id}, Next ID will be {highest_id + 1}")

    except Exception as e:
        print(f"Error tracking test case ID before generation: {e}")

def track_test_case_id_after_generation():
    """
    Track the highest test case ID after generation completes.
    This should be called after test case generation is complete.
    """
    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return

        # Get the current highest test case ID from database
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH

        current_highest_id = get_highest_test_case_id_number(DATABASE_PATH, jira_id, "all")

        # Store the current highest test case ID after generation
        st.session_state[get_session_key('generation_tracking', 'current_highest_test_case_id')] = current_highest_id
        st.session_state[get_session_key('generation_tracking', 'generation_end_id')] = current_highest_id

        print(f"Tracked test case ID after generation: Current highest ID = {current_highest_id}")

    except Exception as e:
        print(f"Error tracking test case ID after generation: {e}")

def get_current_generation_test_case_id_range():
    """
    Get the test case ID range for the current generation session.

    Returns:
        dict: Dictionary containing range information
    """
    range_info = {
        'start_id': None,
        'end_id': None,
        'range_available': False
    }

    try:
        # Get tracked IDs from session state
        start_id = st.session_state.get(get_session_key('generation_tracking', 'generation_start_id'))
        end_id = st.session_state.get(get_session_key('generation_tracking', 'generation_end_id'))

        if start_id is not None and end_id is not None and end_id >= start_id:
            range_info['start_id'] = start_id
            range_info['end_id'] = end_id
            range_info['range_available'] = True

            print(f"Test case ID range available: TC_{start_id:03d} to TC_{end_id:03d}")
        else:
            print("Test case ID range not available or invalid")

    except Exception as e:
        print(f"Error getting test case ID range: {e}")

    return range_info

def track_test_case_id_before_generation():
    """
    Track the highest test case ID before generation starts.
    This should be called before test case generation begins.
    """
    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return

        # Get the highest test case ID from database
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH

        highest_id = get_highest_test_case_id_number(DATABASE_PATH, jira_id, "all")

        # Store the last known test case ID before generation
        st.session_state[get_session_key('generation_tracking', 'last_known_test_case_id')] = highest_id
        st.session_state[get_session_key('generation_tracking', 'generation_start_id')] = highest_id + 1

        print(f"Tracked test case ID before generation: Last known ID = {highest_id}, Next ID will be {highest_id + 1}")

    except Exception as e:
        print(f"Error tracking test case ID before generation: {e}")

def track_test_case_id_after_generation():
    """
    Track the highest test case ID after generation completes.
    This should be called after test case generation is complete.
    """
    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return

        # Get the current highest test case ID from database
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH

        current_highest_id = get_highest_test_case_id_number(DATABASE_PATH, jira_id, "all")

        # Store the current highest test case ID after generation
        st.session_state[get_session_key('generation_tracking', 'current_highest_test_case_id')] = current_highest_id
        st.session_state[get_session_key('generation_tracking', 'generation_end_id')] = current_highest_id

        print(f"Tracked test case ID after generation: Current highest ID = {current_highest_id}")

    except Exception as e:
        print(f"Error tracking test case ID after generation: {e}")

def get_current_generation_test_case_id_range():
    """
    Get the test case ID range for the current generation session using database queries.

    Returns:
        dict: Dictionary containing range information
    """
    range_info = {
        'start_id': None,
        'end_id': None,
        'range_available': False,
        'strategy': 'none'
    }

    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return range_info

        # Strategy 1: Use tracked session state IDs (most reliable)
        start_id = st.session_state.get(get_session_key('generation_tracking', 'generation_start_id'))
        end_id = st.session_state.get(get_session_key('generation_tracking', 'generation_end_id'))

        if start_id is not None and end_id is not None and end_id >= start_id:
            range_info['start_id'] = start_id
            range_info['end_id'] = end_id
            range_info['range_available'] = True
            range_info['strategy'] = 'tracked_session'

            print(f"Test case ID range from tracked session: TC_{start_id:03d} to TC_{end_id:03d}")
            return range_info

        # Strategy 2: Database-driven detection for recent generation
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH
        import sqlite3

        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the most recent test run for this JIRA ID
        cursor.execute("""
            SELECT id, timestamp FROM test_runs
            WHERE jira_id = ?
            ORDER BY timestamp DESC LIMIT 1
        """, (jira_id,))

        recent_run = cursor.fetchone()
        if recent_run:
            # Get test cases for this specific run
            cursor.execute("""
                SELECT test_case_id FROM test_cases
                WHERE test_run_id = ? AND jira_id = ?
                ORDER BY test_case_id
            """, (recent_run['id'], jira_id))

            test_case_ids = cursor.fetchall()
            if test_case_ids:
                # Extract numeric IDs and find range
                numeric_ids = []
                for row in test_case_ids:
                    tc_id = row['test_case_id']
                    if tc_id and tc_id.startswith('TC_'):
                        try:
                            numeric_id = int(tc_id.split('_')[1])
                            numeric_ids.append(numeric_id)
                        except (ValueError, IndexError):
                            continue

                if numeric_ids:
                    numeric_ids.sort()
                    range_info['start_id'] = numeric_ids[0]
                    range_info['end_id'] = numeric_ids[-1]
                    range_info['range_available'] = True
                    range_info['strategy'] = 'database_run'

                    print(f"Test case ID range from database run: TC_{numeric_ids[0]:03d} to TC_{numeric_ids[-1]:03d}")

        conn.close()

    except Exception as e:
        print(f"Error getting test case ID range: {e}")

    return range_info

def get_current_generation_run_info():
    """
    Get precise information about the current test generation run for tight timestamp filtering.

    This function captures the exact generation session timeframe by analyzing:
    1. Test run timestamps from database
    2. Test case creation timestamps
    3. Generation session markers

    Returns:
        dict: Dictionary containing precise run information with tight timestamp bounds
    """
    scenario_data = st.session_state.get("scenario_data", {})

    # Initialize run info with precise timestamp bounds
    run_info = {
        'run_id': None,
        'timestamp_start': None,  # Start of generation session
        'timestamp_end': None,    # End of generation session
        'jira_id': None,
        'test_type': None,
        'generation_session_id': None  # Unique identifier for this generation session
    }

    # Get current generation context
    run_info['jira_id'] = st.session_state.get("jira_case_id_input", "")
    run_info['test_type'] = st.session_state.get("test_type_select", "all")

    # Try to get precise generation session timing
    if scenario_data and scenario_data.get("output_file"):
        try:
            from db_helper import DATABASE_PATH
            import sqlite3
            from datetime import datetime, timedelta

            conn = sqlite3.connect(DATABASE_PATH)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Strategy 1: Find the most recent test run and its associated test cases
            cursor.execute("""
                SELECT tr.id, tr.timestamp, tr.jira_id, tr.test_type
                FROM test_runs tr
                WHERE tr.jira_id = ? AND tr.test_type = ?
                ORDER BY tr.timestamp DESC LIMIT 1
            """, (run_info['jira_id'], run_info['test_type'].lower()))

            recent_run = cursor.fetchone()
            if recent_run:
                run_info['run_id'] = recent_run['id']
                run_timestamp = datetime.strptime(recent_run['timestamp'], "%Y-%m-%d %H:%M:%S")

                # Strategy 2: Get the actual test case creation timestamps for this run
                cursor.execute("""
                    SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest, COUNT(*) as count
                    FROM test_cases
                    WHERE test_run_id = ? AND jira_id = ?
                """, (recent_run['id'], run_info['jira_id']))

                case_timing = cursor.fetchone()
                if case_timing and case_timing['count'] > 0:
                    # Use actual test case creation timestamps for precise bounds
                    earliest_case = datetime.strptime(case_timing['earliest'], "%Y-%m-%d %H:%M:%S")
                    latest_case = datetime.strptime(case_timing['latest'], "%Y-%m-%d %H:%M:%S")

                    # Set tight timestamp bounds (with small buffer for database timing variations)
                    buffer_seconds = 30  # 30-second buffer for timing variations
                    run_info['timestamp_start'] = (earliest_case - timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")
                    run_info['timestamp_end'] = (latest_case + timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")

                    # Create unique generation session identifier
                    run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_{recent_run['id']}_{earliest_case.strftime('%Y%m%d_%H%M%S')}"

                    print(f"Tight timestamp filtering: {run_info['timestamp_start']} to {run_info['timestamp_end']}")
                else:
                    # Fallback: Use test run timestamp with tight window
                    buffer_minutes = 5  # 5-minute window around test run creation
                    start_time = run_timestamp - timedelta(minutes=buffer_minutes)
                    end_time = run_timestamp + timedelta(minutes=buffer_minutes)

                    run_info['timestamp_start'] = start_time.strftime("%Y-%m-%d %H:%M:%S")
                    run_info['timestamp_end'] = end_time.strftime("%Y-%m-%d %H:%M:%S")
                    run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_{recent_run['id']}_fallback"

            # Strategy 3: If no test run found, look for recent test cases directly
            if not run_info['run_id']:
                cursor.execute("""
                    SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest, COUNT(*) as count
                    FROM test_cases
                    WHERE jira_id = ? AND dashboard_test_type = ?
                    AND timestamp >= datetime('now', '-1 hour')
                    ORDER BY timestamp DESC
                """, (run_info['jira_id'], run_info['test_type']))

                recent_cases = cursor.fetchone()
                if recent_cases and recent_cases['count'] > 0:
                    earliest_case = datetime.strptime(recent_cases['earliest'], "%Y-%m-%d %H:%M:%S")
                    latest_case = datetime.strptime(recent_cases['latest'], "%Y-%m-%d %H:%M:%S")

                    # Very tight window for direct case filtering
                    buffer_seconds = 10
                    run_info['timestamp_start'] = (earliest_case - timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")
                    run_info['timestamp_end'] = (latest_case + timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")
                    run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_direct_{earliest_case.strftime('%Y%m%d_%H%M%S')}"

            conn.close()

        except Exception as e:
            print(f"Error getting precise run info: {e}")
            # Fallback to very recent timestamp window
            from datetime import datetime, timedelta
            now = datetime.now()
            run_info['timestamp_start'] = (now - timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S")
            run_info['timestamp_end'] = now.strftime("%Y-%m-%d %H:%M:%S")
            run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_fallback_{now.strftime('%Y%m%d_%H%M%S')}"

    return run_info

def get_smart_default_value(filter_key):
    """
    Get smart default values for filters when showing newly generated test cases.

    Enhanced to provide precise filtering based on current generation run
    instead of entire day filtering.

    Args:
        filter_key (str): The filter key to get smart default for

    Returns:
        Any: Smart default value for the filter
    """
    if filter_key == 'test_type':
        # Use the currently selected test type from generation
        return st.session_state.get("test_type_select", "all")
    elif filter_key == 'ai_generated':
        # Show only AI generated test cases (since they were just generated)
        return 'Yes'
    elif filter_key == 'jira_id':
        # Use the current JIRA ID from generation
        return st.session_state.get("jira_case_id_input", "All")
    elif filter_key == 'enhanced_status':
        # Check if enhanced description was used for generation
        if st.session_state.get("using_enhanced_for_generation", False):
            return 'Yes'
        else:
            return 'All'
    elif filter_key == 'current_run_only':
        # New filter for current generation run
        return True
    else:
        # For other filters, use regular defaults
        return DEFAULT_FILTERS.get(filter_key)

def clear_export_state():
    """
    Clear the export state to prevent stale data issues.
    """
    st.session_state[get_session_key('export', 'ready')] = False
    st.session_state[get_session_key('export', 'filename')] = None
    st.session_state[get_session_key('export', 'format')] = None
    st.session_state[get_session_key('export', 'data')] = None

def clear_all_cache_for_fresh_data():
    """
    Clear all cached data to ensure fresh database calls.
    This function forces the interface to make new database queries.
    """
    # Clear filter options cache
    if 'unified_filter_options' in st.session_state:
        del st.session_state.unified_filter_options
        print("🧹 Cleared filter options cache")

    # Clear any data caching flags
    cache_keys_to_clear = [
        'unified_last_refresh',
        'last_filter_hash',
        'unified_export_ready',
        'unified_export_data',
        'force_refresh',
        'unified_ai_modified_data',
        'unified_ai_modification_applied',
        'ai_modified_data',
        'ai_modification_timestamp'
    ]

    for key in cache_keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]
            print(f"🧹 Cleared session key: {key}")

    # Set force refresh flag
    st.session_state.force_refresh = True
    print("✅ All cache cleared - forcing fresh database calls")

def reset_unified_filters():
    """
    Reset all unified interface filters to their default values.

    Clears all filter session state and resets to default configuration
    as defined in the DEFAULT_FILTERS dictionary.
    """
    # Reset filter values using configuration
    for key, default_value in DEFAULT_FILTERS.items():
        session_key = get_session_key('filters', key)
        if key == 'date_range_days':
            # Reset date range
            start_date, end_date = get_default_date_range()
            st.session_state[get_session_key('filters', 'date_start')] = start_date
            st.session_state[get_session_key('filters', 'date_end')] = end_date
        else:
            st.session_state[session_key] = default_value

    # Reset export state
    clear_export_state()

def get_current_applied_filters() -> Dict:
    """
    Get current applied filter values from session state (used for database queries).

    Returns:
        Dict: Dictionary containing current applied filter values
    """
    filters = {}

    # Get applied filter values from session state - Enhanced with new filters
    applied_test_type = st.session_state.get(get_applied_filter_key('test_type'), 'all')
    if applied_test_type != 'all':
        filters['test_type'] = applied_test_type

    # New high-priority filters based on database analysis
    applied_feature = st.session_state.get(get_applied_filter_key('feature'), 'all')
    if applied_feature != 'all':
        filters['feature'] = applied_feature

    applied_project = st.session_state.get(get_applied_filter_key('project'), 'all')
    if applied_project != 'all':
        filters['project'] = applied_project

    applied_priority = st.session_state.get(get_applied_filter_key('priority'), 'all')
    if applied_priority != 'all':
        filters['priority'] = applied_priority

    applied_test_group = st.session_state.get(get_applied_filter_key('test_group'), 'all')
    if applied_test_group != 'all':
        filters['test_group'] = applied_test_group

    applied_user_name = st.session_state.get(get_applied_filter_key('user_name'), 'all')
    if applied_user_name != 'all':
        filters['user_name'] = applied_user_name

    applied_jira_id = st.session_state.get(get_applied_filter_key('jira_id'), 'All')
    if applied_jira_id != 'All':
        filters['jira_id'] = applied_jira_id

    # Date filters
    applied_date_start = st.session_state.get(get_applied_filter_key('date_start'))
    if applied_date_start:
        if hasattr(applied_date_start, 'strftime'):
            filters['date_start'] = applied_date_start.strftime('%Y-%m-%d')
        else:
            filters['date_start'] = str(applied_date_start)

    applied_date_end = st.session_state.get(get_applied_filter_key('date_end'))
    if applied_date_end:
        if hasattr(applied_date_end, 'strftime'):
            filters['date_end'] = applied_date_end.strftime('%Y-%m-%d')
        else:
            filters['date_end'] = str(applied_date_end)

    # Status filters
    applied_ai_generated = st.session_state.get(get_applied_filter_key('ai_generated'), 'All')
    if applied_ai_generated != 'All':
        filters['ai_generated'] = applied_ai_generated == 'Yes'

    applied_enhanced_status = st.session_state.get(get_applied_filter_key('enhanced_status'), 'All')
    if applied_enhanced_status != 'All':
        filters['enhanced_status'] = applied_enhanced_status == 'Yes'

    applied_is_edited = st.session_state.get(get_applied_filter_key('is_edited'))
    if applied_is_edited is not None:
        filters['is_edited'] = applied_is_edited

    # Test Case ID range filtering (primary strategy for current session detection)
    applied_start_id = st.session_state.get(get_applied_filter_key('test_case_id_range_start'))
    applied_end_id = st.session_state.get(get_applied_filter_key('test_case_id_range_end'))

    if applied_start_id is not None:
        filters['test_case_id_range_start'] = applied_start_id
        print(f"🎯 Applied Test Case ID range start filter: {applied_start_id}")

    if applied_end_id is not None:
        filters['test_case_id_range_end'] = applied_end_id
        print(f"🎯 Applied Test Case ID range end filter: {applied_end_id}")

    # Debug: Show if Test Case ID range filtering is active
    if 'test_case_id_range_start' in filters and 'test_case_id_range_end' in filters:
        start_id = filters['test_case_id_range_start']
        end_id = filters['test_case_id_range_end']
        print(f"🔍 Test Case ID range filtering ACTIVE: TC_{start_id:03d} to TC_{end_id:03d}")
    else:
        print("ℹ️ Test Case ID range filtering NOT active")

    return filters

def get_current_ui_filters() -> Dict:
    """
    Get current UI filter values from session state (displayed in filter controls).

    Returns:
        Dict: Dictionary containing current UI filter values
    """
    ui_filters = {}

    filter_keys = ['test_type', 'feature', 'project', 'priority', 'test_group', 'user_name', 'jira_id',
                   'date_start', 'date_end', 'ai_generated', 'enhanced_status',
                   'is_edited', 'test_case_id_range_start', 'test_case_id_range_end']

    for key in filter_keys:
        ui_filter_key = get_ui_filter_key(key)
        if ui_filter_key in st.session_state:
            ui_filters[key] = st.session_state[ui_filter_key]

    return ui_filters

def count_unique_test_cases(data: pd.DataFrame) -> int:
    """
    Count unique test cases in the filtered data by grouping by Test Case ID.

    Args:
        data (pd.DataFrame): DataFrame containing test case data with potential duplicate rows per test case

    Returns:
        int: Number of unique test cases
    """
    if data.empty:
        return 0

    # Check if Test Case ID column exists
    if 'Test Case ID' not in data.columns:
        print("⚠️ Test Case ID column not found in data")
        return 0

    # Count unique test cases by grouping by Test Case ID
    # Filter out empty Test Case IDs (which represent additional test steps)
    test_case_data = data[data['Test Case ID'].notna() & (data['Test Case ID'] != '')]
    unique_test_cases = test_case_data['Test Case ID'].nunique()

    print(f"📊 Counted {unique_test_cases} unique test cases from {len(data)} total rows")
    return unique_test_cases

def get_test_case_summary(data: pd.DataFrame) -> Dict:
    """
    Get a summary of test cases including unique count and breakdown by type.

    Args:
        data (pd.DataFrame): DataFrame containing test case data

    Returns:
        Dict: Summary information including unique count, total rows, and type breakdown
    """
    if data.empty:
        return {
            'unique_test_cases': 0,
            'total_rows': 0,
            'test_types': {},
            'jira_ids': set()
        }

    # Check if required columns exist
    if 'Test Case ID' not in data.columns:
        print("⚠️ Test Case ID column not found in data for summary")
        return {
            'unique_test_cases': 0,
            'total_rows': len(data),
            'test_types': {},
            'jira_ids': set()
        }

    # Get unique test cases (rows with non-empty Test Case ID)
    test_case_rows = data[data['Test Case ID'].notna() & (data['Test Case ID'] != '')]

    unique_count = test_case_rows['Test Case ID'].nunique()
    total_rows = len(data)

    # Get test type breakdown
    test_types = {}
    if 'Test Type' in test_case_rows.columns and not test_case_rows.empty:
        # Filter out empty test types
        type_data = test_case_rows[test_case_rows['Test Type'].notna() & (test_case_rows['Test Type'] != '')]
        if not type_data.empty:
            type_counts = type_data.groupby('Test Type')['Test Case ID'].nunique()
            test_types = type_counts.to_dict()

    # Get unique JIRA IDs
    jira_ids = set()
    if 'User Story ID' in test_case_rows.columns and not test_case_rows.empty:
        # Filter out empty JIRA IDs
        jira_data = test_case_rows[test_case_rows['User Story ID'].notna() & (test_case_rows['User Story ID'] != '')]
        if not jira_data.empty:
            jira_ids = set(jira_data['User Story ID'].unique())

    summary = {
        'unique_test_cases': unique_count,
        'total_rows': total_rows,
        'test_types': test_types,
        'jira_ids': jira_ids
    }

    print(f"📈 Test case summary: {unique_count} unique test cases, {total_rows} total rows")
    if test_types:
        print(f"📊 Test type breakdown: {test_types}")

    return summary

def get_current_filters() -> Dict:
    """
    Get current applied filter values (for backward compatibility).
    This function now delegates to get_current_applied_filters().

    Returns:
        Dict: Dictionary containing current applied filter values
    """
    return get_current_applied_filters()

def render_filter_action_buttons():
    """
    Render the Apply Filters and Reset Filters buttons.

    Returns:
        tuple: (apply_clicked, reset_clicked) - Boolean values indicating button clicks
    """
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        # Show current filter status
        applied_filters = get_current_applied_filters()
        ui_filters = get_current_ui_filters()

        # Check if UI filters differ from applied filters
        filters_changed = False
        for key in ['test_type', 'date_start', 'date_end', 'ai_generated', 'enhanced_status',
                   'jira_id', 'test_case_id_range_start', 'test_case_id_range_end']:
            ui_value = ui_filters.get(key)
            applied_key = key
            if key in ['date_start', 'date_end'] and ui_value:
                # Convert date to string for comparison
                if hasattr(ui_value, 'strftime'):
                    ui_value = ui_value.strftime('%Y-%m-%d')

            applied_value = applied_filters.get(applied_key)
            if key == 'ai_generated' and applied_value is not None:
                applied_value = 'Yes' if applied_value else 'No'
            elif key == 'enhanced_status' and applied_value is not None:
                applied_value = 'Yes' if applied_value else 'No'
            elif key == 'test_type' and applied_value is None:
                applied_value = 'all'
            elif key in ['ai_generated', 'enhanced_status'] and applied_value is None:
                applied_value = 'All'
            elif key == 'jira_id' and applied_value is None:
                applied_value = 'All'

            if ui_value != applied_value:
                filters_changed = True
                break

        if filters_changed:
            st.info("🔄 **Filters changed** - Click 'Apply Filters' to update results")
        else:
            st.success("✅ **Filters applied** - Results are up to date")

    with col2:
        apply_clicked = st.button(
            "🔍 Apply Filters",
            help="Apply current filter settings to update the test cases table",
            use_container_width=True,
            type="primary"
        )

    with col3:
        reset_clicked = st.button(
            "🔄 Reset Filters",
            help="Reset all filters to smart defaults from generation",
            use_container_width=True
        )

    return apply_clicked, reset_clicked

def render_count_indicator(filtered_count: int, total_count: int):
    """
    Render the count indicator showing filtered vs total test cases.

    Args:
        filtered_count (int): Number of filtered test cases
        total_count (int): Total number of test cases
    """
    col1, col2 = st.columns([4, 1])

    with col1:
        # Clean count display without emoji or excessive styling
        if filtered_count == total_count:
            st.markdown(f"**{filtered_count:,} test cases**")
        else:
            st.markdown(f"**{filtered_count:,}** of **{total_count:,}** test cases")

    with col2:
        if st.button("🔄 Refresh", help="Force fresh database query", use_container_width=True):
            # Clear all cached data to force fresh database calls
            clear_all_cache_for_fresh_data()
            st.rerun()

def render_filter_status_indicator():
    """
    Render a detailed filter status indicator showing which filters are currently applied.
    """
    applied_filters = get_current_applied_filters()

    if not applied_filters:
        st.info("📊 **No filters applied** - Showing all test cases")
        return

    # Build filter status message
    status_parts = []

    if 'test_type' in applied_filters:
        status_parts.append(f"Type: {applied_filters['test_type'].title()}")

    if 'date_start' in applied_filters and 'date_end' in applied_filters:
        status_parts.append(f"Date: {applied_filters['date_start']} to {applied_filters['date_end']}")

    if 'jira_id' in applied_filters:
        status_parts.append(f"JIRA: {applied_filters['jira_id']}")

    if 'ai_generated' in applied_filters:
        ai_status = "Yes" if applied_filters['ai_generated'] else "No"
        status_parts.append(f"AI Generated: {ai_status}")

    if 'enhanced_status' in applied_filters:
        enhanced_status = "Yes" if applied_filters['enhanced_status'] else "No"
        status_parts.append(f"Enhanced: {enhanced_status}")

    if 'test_case_id_range_start' in applied_filters and 'test_case_id_range_end' in applied_filters:
        start_id = applied_filters['test_case_id_range_start']
        end_id = applied_filters['test_case_id_range_end']
        status_parts.append(f"ID Range: TC_{start_id:03d} to TC_{end_id:03d}")

    if status_parts:
        filter_status = " | ".join(status_parts)
        st.success(f"🔍 **Active filters:** {filter_status}")
    else:
        st.info("📊 **No specific filters applied** - Showing all test cases")

def render_count_indicator_with_refresh(filtered_data: pd.DataFrame, total_data: pd.DataFrame, refresh_timestamp: str):
    """
    Enhanced count indicator with timestamp and refresh functionality.
    Now uses unique test case counting instead of raw row counting.

    Args:
        filtered_data (pd.DataFrame): Filtered test case data
        total_data (pd.DataFrame): Total test case data
        refresh_timestamp (str): Timestamp of last database refresh
    """
    # Count unique test cases instead of raw rows
    filtered_count = count_unique_test_cases(filtered_data)
    total_count = count_unique_test_cases(total_data)

    # Get detailed summary for debugging
    filtered_summary = get_test_case_summary(filtered_data)
    total_summary = get_test_case_summary(total_data)

    col1, col2 = st.columns([4, 1])

    with col1:
        # Clean count display with timestamp
        if filtered_count == total_count:
            st.markdown(f"**{filtered_count:,} test cases**")
        else:
            st.markdown(f"**{filtered_count:,}** of **{total_count:,}** test cases")

        # Show additional details in caption
        if filtered_count != len(filtered_data):
            st.caption(f"🕒 Last refreshed: {refresh_timestamp} | {len(filtered_data):,} total rows (including test steps)")
        else:
            st.caption(f"🕒 Last refreshed: {refresh_timestamp}")

    with col2:
        if st.button("🔄 Refresh", help="Force fresh database query", use_container_width=True):
            # Clear all cached data to force fresh database calls
            clear_all_cache_for_fresh_data()
            st.rerun()

    # Show detailed filter status
    render_filter_status_indicator()

    # Show test type breakdown if available
    if filtered_summary['test_types']:
        with st.expander("📊 Test Case Breakdown", expanded=False):
            for test_type, count in filtered_summary['test_types'].items():
                st.write(f"• **{test_type.title()}**: {count} test cases")

            if filtered_summary['jira_ids']:
                st.write(f"• **JIRA Stories**: {len(filtered_summary['jira_ids'])} unique stories")
                if len(filtered_summary['jira_ids']) <= 5:  # Show JIRA IDs if not too many
                    jira_list = ", ".join(sorted(filtered_summary['jira_ids']))
                    st.caption(f"Stories: {jira_list}")

def render_basic_filters(filter_options: Dict):
    """
    Render the basic filter controls that are always visible.
    Uses UI filter state that doesn't automatically update the table.

    Args:
        filter_options (Dict): Available filter options from database
    """
    st.subheader("🔍 Basic Filters")

    with st.container():
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            # Test Type filter - use UI state
            test_type_options = ['all'] + filter_options.get('test_types', [])
            ui_test_type_key = get_ui_filter_key('test_type')
            current_ui_test_type = st.session_state.get(ui_test_type_key, 'all')

            st.session_state[ui_test_type_key] = st.selectbox(
                "Test Type:",
                options=test_type_options,
                index=test_type_options.index(current_ui_test_type)
                      if current_ui_test_type in test_type_options else 0,
                key="unified_ui_type_filter_select",
                help="Filter by test case type (POSITIVE, NEGATIVE, SECURITY, PERFORMANCE)"
            )

        with col2:
            # Feature filter - use UI state (HIGH PRIORITY based on analysis)
            feature_options = ['all'] + filter_options.get('features', [])
            ui_feature_key = get_ui_filter_key('feature')
            current_ui_feature = st.session_state.get(ui_feature_key, 'all')

            st.session_state[ui_feature_key] = st.selectbox(
                "Feature:",
                options=feature_options,
                index=feature_options.index(current_ui_feature)
                      if current_ui_feature in feature_options else 0,
                key="unified_ui_feature_filter_select",
                help="Filter by feature being tested"
            )

        with col3:
            # Project filter - use UI state (HIGH PRIORITY based on analysis)
            project_options = ['all'] + filter_options.get('projects', [])
            ui_project_key = get_ui_filter_key('project')
            current_ui_project = st.session_state.get(ui_project_key, 'all')

            st.session_state[ui_project_key] = st.selectbox(
                "Project:",
                options=project_options,
                index=project_options.index(current_ui_project)
                      if current_ui_project in project_options else 0,
                key="unified_ui_project_filter_select",
                help="Filter by project name"
            )

        with col4:
            # Priority filter - use UI state (based on database analysis)
            priority_options = ['all'] + filter_options.get('priorities', [])
            ui_priority_key = get_ui_filter_key('priority')
            current_ui_priority = st.session_state.get(ui_priority_key, 'all')

            st.session_state[ui_priority_key] = st.selectbox(
                "Priority:",
                options=priority_options,
                index=priority_options.index(current_ui_priority)
                      if current_ui_priority in priority_options else 0,
                key="unified_ui_priority_filter_select",
                help="Filter by test case priority level"
            )

def render_advanced_filters(filter_options: Dict):
    """
    Render the advanced filter controls in a collapsible expander.
    Uses UI filter state that doesn't automatically update the table.

    Args:
        filter_options (Dict): Available filter options from database
    """
    with st.expander("🔧 Advanced Filters", expanded=False):
        # First row: High-priority filters based on database analysis
        col1, col2, col3 = st.columns(3)

        with col1:
            # Test Group filter - use UI state (HIGH PRIORITY based on analysis)
            test_group_options = ['all'] + filter_options.get('test_groups', [])
            ui_test_group_key = get_ui_filter_key('test_group')
            current_ui_test_group = st.session_state.get(ui_test_group_key, 'all')

            st.session_state[ui_test_group_key] = st.selectbox(
                "Test Group:",
                options=test_group_options,
                index=test_group_options.index(current_ui_test_group)
                      if current_ui_test_group in test_group_options else 0,
                key="unified_ui_test_group_filter_select",
                help="Filter by test group classification"
            )

        with col2:
            # User Name filter - use UI state (HIGH PRIORITY based on analysis)
            user_options = ['all'] + filter_options.get('users', [])
            ui_user_key = get_ui_filter_key('user_name')
            current_ui_user = st.session_state.get(ui_user_key, 'all')

            st.session_state[ui_user_key] = st.selectbox(
                "User:",
                options=user_options,
                index=user_options.index(current_ui_user)
                      if current_ui_user in user_options else 0,
                key="unified_ui_user_filter_select",
                help="Filter by user who created the test cases"
            )

        with col3:
            # JIRA ID filter - use UI state (moved from basic due to limited variety)
            ui_jira_id_key = get_ui_filter_key('jira_id')
            current_ui_jira_id = st.session_state.get(ui_jira_id_key, 'All')

            jira_options = ['All'] + filter_options.get('jira_ids', [])
            jira_index = jira_options.index(current_ui_jira_id) if current_ui_jira_id in jira_options else 0

            st.session_state[ui_jira_id_key] = st.selectbox(
                "JIRA User Story ID:",
                options=jira_options,
                index=jira_index,
                key="unified_ui_jira_id_filter",
                help="Filter by specific JIRA User Story ID"
            )

        # Second row: Date filters and status filters
        st.markdown("---")
        col4, col5, col6 = st.columns(3)

        with col4:
            # Date range start - use UI state
            ui_date_start_key = get_ui_filter_key('date_start')
            current_ui_date_start = st.session_state.get(ui_date_start_key, datetime.now().date() - timedelta(days=30))

            st.session_state[ui_date_start_key] = st.date_input(
                "Start Date:",
                value=current_ui_date_start,
                key="unified_ui_date_start_filter",
                help="Filter test cases from this date onwards"
            )

        with col5:
            # Date range end - use UI state
            ui_date_end_key = get_ui_filter_key('date_end')
            current_ui_date_end = st.session_state.get(ui_date_end_key, datetime.now().date())

            st.session_state[ui_date_end_key] = st.date_input(
                "End Date:",
                value=current_ui_date_end,
                key="unified_ui_date_end_filter",
                help="Filter test cases up to this date"
            )

        with col6:
            # AI Generated filter - use UI state
            ui_ai_generated_key = get_ui_filter_key('ai_generated')
            current_ui_ai_generated = st.session_state.get(ui_ai_generated_key, 'All')

            st.session_state[ui_ai_generated_key] = st.selectbox(
                "AI Generated:",
                options=['All', 'Yes', 'No'],
                index=['All', 'Yes', 'No'].index(current_ui_ai_generated),
                key="unified_ui_ai_generated_filter",
                help="Filter by whether test cases were generated by AI"
            )

        # Add Test Case ID Range filters in a new row
        st.markdown("---")
        col4, col5, col6 = st.columns(3)

        with col4:
            # Test Case ID Range Start - use UI state
            ui_range_start_key = get_ui_filter_key('test_case_id_range_start')
            current_ui_range_start = st.session_state.get(ui_range_start_key)

            st.session_state[ui_range_start_key] = st.number_input(
                "Test Case ID Range Start:",
                min_value=1,
                max_value=9999,
                value=current_ui_range_start if current_ui_range_start is not None else 1,
                step=1,
                key="unified_ui_test_case_id_range_start",
                help="Start of Test Case ID range (e.g., 46 for TC_046)"
            )

        with col5:
            # Test Case ID Range End - use UI state
            ui_range_end_key = get_ui_filter_key('test_case_id_range_end')
            current_ui_range_end = st.session_state.get(ui_range_end_key)

            st.session_state[ui_range_end_key] = st.number_input(
                "Test Case ID Range End:",
                min_value=1,
                max_value=9999,
                value=current_ui_range_end if current_ui_range_end is not None else 100,
                step=1,
                key="unified_ui_test_case_id_range_end",
                help="End of Test Case ID range (e.g., 55 for TC_055)"
            )

        with col6:
            # Show current range if set
            if current_ui_range_start is not None and current_ui_range_end is not None:
                st.markdown("**Current Range:**")
                st.code(f"TC_{current_ui_range_start:03d} to TC_{current_ui_range_end:03d}")
            else:
                st.markdown("**Range Status:**")
                st.info("No ID range set")

def render_data_table(filtered_data: pd.DataFrame) -> pd.DataFrame:
    """
    Render the editable data table with professional styling.

    Args:
        filtered_data (pd.DataFrame): Filtered test case data

    Returns:
        pd.DataFrame: Edited dataframe from user interactions
    """
    if filtered_data.empty:
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
            <h3 style="color: #6c757d; margin-bottom: 1rem;">⚠️ No test cases match the current filter criteria</h3>
            <p style="color: #6c757d; margin-bottom: 1.5rem;">Try adjusting your filters or resetting them to see more data.</p>
            <p style="color: #495057; font-size: 0.9rem;">💡 <strong>Tip:</strong> Use broader date ranges or select 'All' for categorical filters</p>
        </div>
        """, unsafe_allow_html=True)
        return pd.DataFrame()

    # Display count information with AI modification stats
    unique_count = count_unique_test_cases(filtered_data)
    total_rows = len(filtered_data)

    # Count AI-modified test cases if the column exists
    ai_modified_count = 0
    if 'ai_modified' in filtered_data.columns:
        ai_modified_count = len(filtered_data[filtered_data['ai_modified'] == 1]['Test Case ID'].dropna().unique())

    # Display header with AI indicator
    header_text = "📊 Test Cases"
    if ai_modified_count > 0:
        header_text += f" (🤖 {ai_modified_count} AI-modified)"

    st.subheader(header_text)

    # Display detailed counts
    count_text = f"**Showing {unique_count} unique test cases ({total_rows} total rows)**"
    if ai_modified_count > 0:
        count_text += f" • 🤖 {ai_modified_count} AI-modified"
    st.markdown(count_text)

    # Configure editable columns based on JIRA terminology
    editable_columns = [
        "Test Case Objective", "Prerequisite", "Test Steps",
        "Expected Result", "Test Status", "Priority", "Feature"
    ]

    # Add AI modification indicator column for display
    display_data = filtered_data.copy()
    if 'ai_modified' in display_data.columns and 'modification_source' in display_data.columns:
        display_data['AI Status'] = display_data.apply(lambda row:
            "🤖 AI Modified" if row.get('ai_modified') == 1
            else "✏️ Manual" if row.get('modification_source') == 'manual'
            else "📥 Imported", axis=1)
    elif 'ai_modified' in display_data.columns:
        display_data['AI Status'] = display_data.apply(lambda row:
            "🤖 AI Modified" if row.get('ai_modified') == 1
            else "✏️ Manual", axis=1)

    column_config = {}
    for col in display_data.columns:
        if col in editable_columns:
            column_config[col] = st.column_config.TextColumn(
                col,
                disabled=False,
                help=f"Click to edit {col.lower()}"
            )
        elif col == "AI Status":
            column_config[col] = st.column_config.TextColumn(
                "AI Status",
                disabled=True,
                help="Shows if test case was modified by AI",
                width="small"
            )
        else:
            column_config[col] = st.column_config.TextColumn(
                col,
                disabled=True,
                help=f"Read-only: {col}"
            )

    # Display editable table with professional styling
    with st.container():
        # Use the original column order from the two-tab system
        from .unified_interface_config import COLUMN_ORDER

        # Filter column order to only include columns that exist in the data
        available_columns = [col for col in COLUMN_ORDER if col in display_data.columns]

        # Add AI Status column to the end if it exists
        if 'AI Status' in display_data.columns and 'AI Status' not in available_columns:
            available_columns.append('AI Status')

        # Hide internal AI tracking columns from display
        columns_to_hide = ['ai_modified', 'modification_source', 'ai_model_used',
                          'ai_modification_timestamp', 'ai_modification_user', 'ai_user_query']

        display_columns = [col for col in available_columns if col not in columns_to_hide]
        display_data_filtered = display_data[display_columns]

        edited_df = st.data_editor(
            display_data_filtered,
            use_container_width=True,
            num_rows="dynamic",
            column_config=column_config,
            hide_index=True,
            height=500,
            key="unified_test_cases_editor",
            column_order=display_columns
        )

    # Return the original structure (without AI Status column) for consistency
    if 'AI Status' in edited_df.columns:
        edited_df = edited_df.drop('AI Status', axis=1)

    # Merge back the hidden columns if they existed
    for col in columns_to_hide:
        if col in filtered_data.columns:
            edited_df[col] = filtered_data[col]

    return edited_df

def validate_ai_modification_output(original_df, modified_df):
    """
    Enhanced validation logic for AI modification output with read-only field protection.
    Prevents AI from modifying protected fields while allowing legitimate enhancements.
    """
    # Define read-only fields that should be protected from AI modification
    READ_ONLY_FIELDS = [
        'User Story ID',      # Primary protected field (reported issue)
        'Test Case ID',       # Should never change
        'Project',            # System-assigned
        'Timestamp',          # System-generated
        'User Name',          # System-assigned
        'Username',           # Alternative field name
        'Actual Result',      # Test execution result
        'Defect ID',          # Defect tracking field
        'Test Type',          # System classification
        'Test Group',         # System classification
        'Step No',            # System-generated sequence
        'Comments',           # May contain system data
        # AI metadata fields (should never be modified by AI)
        'ai_modified',
        'modification_source',
        'ai_model_used',
        'ai_modification_timestamp',
        'ai_modification_user',
        'ai_user_query'
    ]

    # Define explicitly editable fields (consistent with manual editing)
    EDITABLE_FIELDS = [
        'Test Case Objective',
        'Prerequisite',
        'Test Steps',
        'Expected Result',
        'Test Status',
        'Priority',
        'Feature'
    ]

    # 1. Check if essential columns are present
    essential_columns = ['Test Case ID', 'Test Case Objective', 'Test Steps', 'Expected Result']
    missing_essential = [col for col in essential_columns if col not in modified_df.columns]

    if missing_essential:
        return False, f"Missing essential columns: {missing_essential}"

    # 2. Check if all original columns are preserved
    missing_columns = set(original_df.columns) - set(modified_df.columns)
    if missing_columns:
        return False, f"Missing original columns: {list(missing_columns)}"

    # 3. CRITICAL: Validate read-only fields are not modified
    modified_readonly_fields = []
    for field in READ_ONLY_FIELDS:
        if field in original_df.columns and field in modified_df.columns:
            try:
                # Handle cases where DataFrame sizes differ (AI added/removed rows)
                # Group by Test Case ID to compare read-only fields per test case
                if 'Test Case ID' in original_df.columns and 'Test Case ID' in modified_df.columns:
                    # Get unique test case IDs from both DataFrames
                    original_test_ids = set(original_df['Test Case ID'].dropna().astype(str))
                    modified_test_ids = set(modified_df['Test Case ID'].dropna().astype(str))

                    # Check common test case IDs for read-only field changes
                    common_test_ids = original_test_ids & modified_test_ids

                    for test_id in common_test_ids:
                        if test_id and test_id != '' and test_id != 'nan':
                            # Get the first occurrence of this test case ID in each DataFrame
                            orig_mask = original_df['Test Case ID'].astype(str) == test_id
                            mod_mask = modified_df['Test Case ID'].astype(str) == test_id

                            if orig_mask.any() and mod_mask.any():
                                orig_value = str(original_df[orig_mask][field].iloc[0]).strip()
                                mod_value = str(modified_df[mod_mask][field].iloc[0]).strip()

                                # Compare values (ignore empty/nan differences)
                                if orig_value != mod_value and not (
                                    orig_value in ['', 'nan', 'None'] and mod_value in ['', 'nan', 'None']
                                ):
                                    modified_readonly_fields.append(f"{field} (Test Case {test_id}: '{orig_value}' → '{mod_value}')")
                else:
                    # Fallback: Direct comparison for cases without Test Case ID
                    # Only compare if DataFrames have the same length
                    if len(original_df) == len(modified_df):
                        original_values = original_df[field].fillna('').astype(str)
                        modified_values = modified_df[field].fillna('').astype(str)

                        if not original_values.equals(modified_values):
                            changes_mask = original_values != modified_values
                            changed_count = changes_mask.sum()
                            if changed_count > 0:
                                modified_readonly_fields.append(f"{field} ({changed_count} changes)")

            except Exception as e:
                # If comparison fails, log the error but don't block the modification
                print(f"⚠️ Warning: Could not validate read-only field '{field}': {e}")
                continue

    if modified_readonly_fields:
        return False, f"AI attempted to modify read-only fields: {', '.join(modified_readonly_fields)}. Only these fields can be modified: {', '.join(EDITABLE_FIELDS)}"

    # 4. Validate Test Case IDs are preserved (enhanced check)
    if 'Test Case ID' in original_df.columns and 'Test Case ID' in modified_df.columns:
        # Filter out NaN and empty strings
        original_test_ids = set(original_df['Test Case ID'].dropna().astype(str).replace('', pd.NA).dropna().unique())
        modified_test_ids = set(modified_df['Test Case ID'].dropna().astype(str).replace('', pd.NA).dropna().unique())

        # Only check if we have actual test case IDs to validate
        if original_test_ids:
            missing_test_ids = original_test_ids - modified_test_ids
            if missing_test_ids:
                return False, f"Missing test case IDs: {list(missing_test_ids)}"

    # 5. Check for reasonable row count changes (allow up to 5x increase)
    row_ratio = len(modified_df) / len(original_df) if len(original_df) > 0 else 1
    if row_ratio > 5.0:
        return False, f"Excessive row increase: {len(original_df)} → {len(modified_df)} ({row_ratio:.1f}x)"

    # 6. Check for empty critical fields
    critical_fields = ['Test Case Objective', 'Test Steps', 'Expected Result']
    for field in critical_fields:
        if field in modified_df.columns:
            empty_count = modified_df[field].isna().sum() + (modified_df[field] == '').sum()
            if empty_count > len(modified_df) * 0.8:  # More than 80% empty
                return False, f"Too many empty {field} values: {empty_count}/{len(modified_df)}"

    return True, "Validation passed - only editable fields were modified"

def validate_user_query_for_readonly_fields(user_query: str) -> tuple[bool, str]:
    """
    Pre-validate user query to detect attempts to modify read-only fields.
    Provides early warning before sending to AI.

    Args:
        user_query (str): User's modification request

    Returns:
        tuple: (is_valid, warning_message)
    """
    # Define read-only field patterns to detect in user queries
    readonly_field_patterns = {
        'User Story ID': ['user story id', 'story id', 'user story', 'jira id', 'jira story'],
        'Test Case ID': ['test case id', 'tc id', 'test id', 'case id'],
        'Project': ['project name', 'project'],
        'Timestamp': ['timestamp', 'date created', 'creation date'],
        'Test Type': ['test type', 'type of test'],
        'Test Group': ['test group', 'group'],
        'Defect ID': ['defect id', 'bug id', 'defect number'],
        'Actual Result': ['actual result', 'actual outcome']
    }

    # Define action patterns that suggest modification attempts
    modification_patterns = [
        'replace', 'change', 'update', 'modify', 'set', 'assign',
        'rename', 'alter', 'switch', 'convert', 'transform'
    ]

    user_query_lower = user_query.lower()
    detected_issues = []

    # Check for modification attempts on read-only fields
    for field_name, field_patterns in readonly_field_patterns.items():
        for field_pattern in field_patterns:
            if field_pattern in user_query_lower:
                # Check if there's a modification action nearby
                for action in modification_patterns:
                    if action in user_query_lower:
                        # Check proximity (within 20 characters)
                        field_pos = user_query_lower.find(field_pattern)
                        action_pos = user_query_lower.find(action)
                        if abs(field_pos - action_pos) <= 20:
                            detected_issues.append(field_name)
                            break
                break

    if detected_issues:
        editable_fields = [
            'Test Case Objective', 'Prerequisite', 'Test Steps',
            'Expected Result', 'Test Status', 'Priority', 'Feature'
        ]

        warning_msg = f"""
⚠️ **Read-Only Field Protection Warning**

Your request appears to attempt modifying these read-only fields: **{', '.join(detected_issues)}**

**Read-only fields cannot be modified** to maintain data integrity.

**You can only modify these fields:**
• Test Case Objective
• Prerequisite
• Test Steps
• Expected Result
• Test Status
• Priority
• Feature

Please rephrase your request to focus on modifying only the editable fields listed above.
        """.strip()

        return False, warning_msg

    return True, ""

def render_ai_modification_section(filtered_data: pd.DataFrame) -> pd.DataFrame:
    """
    Render the AI-powered test case modification interface for the unified interface.

    Args:
        filtered_data (pd.DataFrame): Current filtered test case data

    Returns:
        pd.DataFrame: Modified test cases dataframe (same as input if no changes)
    """
    if filtered_data.empty:
        return filtered_data

    # Import required functions
    from helpers.ai.llm_providers import modify_test_cases_with_ai

    # Get AI settings from session state (same pattern as test_analysis.py)
    selected_model = st.session_state.get("selected_model_persistent", "gemini-2.0-flash")
    google_api_key = st.session_state.get("google_api_key", "")
    ai_provider = st.session_state.get("ai_provider_persistent", "Cloud")

    # Fallback: Load API key from config if not in session state
    if not google_api_key:
        try:
            import os
            import json
            config_path = os.path.join('..', 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    google_api_key = config.get('google_api_key', '')
                    # Store in session state for future use
                    st.session_state["google_api_key"] = google_api_key
                    print(f"🔧 DEBUG: Loaded API key from config fallback (length: {len(google_api_key)})")
            else:
                print(f"🔧 DEBUG: Config file not found at {config_path}")
        except Exception as e:
            st.error(f"Error loading API key from config: {e}")
            print(f"🔧 DEBUG: Error loading API key: {e}")
            google_api_key = ""
    else:
        print(f"🔧 DEBUG: API key found in session state (length: {len(google_api_key)})")

    # Get JIRA issue from session state if available
    jira_issue = st.session_state.get("jira_issue")

    st.markdown("---")
    st.subheader("🤖 AI Test Case Modification")

    # Create an expander for the modification interface (collapsed by default to reduce clutter)
    with st.expander("✨ Modify Test Cases with AI", expanded=False):
        st.markdown("""
        **Use AI to modify your test cases** - Describe what changes you want to make
        and the AI will update your test cases accordingly.

        **Example queries:**
        - "Add more security-focused test cases"
        - "Make the test steps more detailed"
        - "Add edge cases for input validation"
        - "Include performance considerations"
        - "Add negative test scenarios"
        """)

        # User query input
        user_query = st.text_area(
            "Describe the changes you want to make:",
            placeholder="e.g., Add more detailed test steps for each scenario...",
            key="unified_modification_query",
            height=100
        )

        # Modification options
        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            # AI provider info
            st.info(f"🌟 Using {ai_provider} ({selected_model})")

        with col2:
            # Show current test case count
            test_case_count = count_unique_test_cases(filtered_data)
            st.info(f"📝 Current test cases: {test_case_count}")

        with col3:
            # Show filtered status
            if len(filtered_data) > 0:
                st.info(f"📊 Modifying filtered data")
            else:
                st.warning("⚠️ No data to modify")

        # Check API key availability for Cloud provider
        api_key_available = True
        if ai_provider == "Cloud" and not google_api_key:
            api_key_available = False
            st.error("🔑 Google API key is required for Cloud AI provider. Please check your configuration.")
            st.info("💡 The API key should be configured in the same way as for test case generation.")

        # Modify button
        if st.button(
            "🚀 Apply AI Modifications",
            key="unified_apply_modifications",
            disabled=not user_query.strip() or filtered_data.empty or not api_key_available,
            help="Click to apply the requested modifications using AI" if api_key_available else "API key required for AI modifications",
            use_container_width=True
        ):
            if not user_query.strip():
                st.warning("Please enter a modification request.")
                return filtered_data

            # Pre-validate user query for read-only field modification attempts
            query_valid, warning_message = validate_user_query_for_readonly_fields(user_query)
            if not query_valid:
                st.error("🚫 **Read-Only Field Protection**")
                st.markdown(warning_message)
                return filtered_data

            # Get JIRA description for context
            jira_description = ""
            if jira_issue:
                jira_description = f"Summary: {jira_issue.fields.summary}\n"
                if hasattr(jira_issue.fields, 'description') and jira_issue.fields.description:
                    jira_description += f"Description: {jira_issue.fields.description}"

            # Show progress
            with st.spinner("🤖 AI is analyzing and modifying your test cases..."):
                try:
                    # Validate API key before making the call
                    if ai_provider == "Cloud" and not google_api_key:
                        st.error("🔑 Google API key is missing. Please check your configuration.")
                        return filtered_data

                    # Debug logging before AI call
                    print(f"🔧 DEBUG: About to call AI modification with:")
                    print(f"   - AI Provider: {ai_provider}")
                    print(f"   - Model: {selected_model}")
                    print(f"   - API Key Length: {len(google_api_key) if google_api_key else 0}")
                    print(f"   - API Key Present: {'Yes' if google_api_key else 'No'}")
                    if google_api_key:
                        print(f"   - API Key Starts With: {google_api_key[:10]}...")

                    # Call AI modification function
                    success, modified_df, error_message = modify_test_cases_with_ai(
                        test_cases_df=filtered_data,
                        jira_description=jira_description,
                        user_query=user_query,
                        model=selected_model,
                        google_api_key=google_api_key
                    )

                    if success:
                        # Show AI modification success with details
                        original_test_cases = len(filtered_data['Test Case ID'].dropna().unique()) if 'Test Case ID' in filtered_data.columns else 0

                        st.success("✅ Test cases modified successfully!")

                        # Show enhancement details
                        if len(modified_df) > len(filtered_data):
                            st.info(f"📈 Enhanced test coverage: {len(filtered_data)} → {len(modified_df)} rows ({original_test_cases} test cases)")
                        elif len(modified_df) < len(filtered_data):
                            st.info(f"📉 Streamlined test cases: {len(filtered_data)} → {len(modified_df)} rows ({original_test_cases} test cases)")
                        else:
                            st.info(f"🔄 Modified {original_test_cases} test cases ({len(modified_df)} rows)")

                        # Validate AI output structure before accepting changes
                        try:
                            is_valid, validation_message = validate_ai_modification_output(filtered_data, modified_df)
                            if not is_valid:
                                st.error(f"❌ AI output validation failed: {validation_message}")
                                st.info("💡 The AI modification was rejected to maintain data integrity. Please try rephrasing your request.")

                                # Provide helpful debugging information
                                with st.expander("🔍 Debug Information", expanded=False):
                                    st.write("**Original Data Shape:**", filtered_data.shape)
                                    st.write("**Modified Data Shape:**", modified_df.shape)
                                    st.write("**Original Columns:**", list(filtered_data.columns))
                                    st.write("**Modified Columns:**", list(modified_df.columns))

                                return filtered_data
                        except Exception as validation_error:
                            st.error(f"❌ Validation error: {str(validation_error)}")
                            st.info("💡 There was an issue validating the AI output. Please try again.")

                            # Provide debugging information for validation errors
                            with st.expander("🔍 Validation Error Details", expanded=False):
                                st.write("**Error:**", str(validation_error))
                                st.write("**Original Data Shape:**", filtered_data.shape)
                                st.write("**Modified Data Shape:**", modified_df.shape if 'modified_df' in locals() else "N/A")

                            return filtered_data

                        # Show what changed
                        original_count = len(filtered_data)
                        modified_count = len(modified_df)
                        user_query_lower = user_query.lower()

                        if modified_count != original_count:
                            # Only show add/remove if user explicitly asked for it
                            if any(kw in user_query_lower for kw in ["add", "remove", "delete", "insert"]):
                                if modified_count > original_count:
                                    st.info(f"📈 Added {modified_count - original_count} new test cases")
                                else:
                                    st.info(f"📉 Removed {original_count - modified_count} test cases")
                            else:
                                st.warning("⚠️ The number of test cases changed, but your request did not mention adding or removing test cases. Only content should have been updated. Please review the changes.")
                        else:
                            st.info("📝 Test cases updated in place")

                        # Store the modification in session state for the unified interface
                        st.session_state["unified_ai_modified_data"] = modified_df.copy()
                        st.session_state["unified_ai_modification_applied"] = True

                        # Store modification history
                        if "unified_modification_history" not in st.session_state:
                            st.session_state["unified_modification_history"] = []

                        st.session_state["unified_modification_history"].append({
                            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "query": user_query,
                            "ai_provider": ai_provider,
                            "model": selected_model,
                            "original_count": original_count,
                            "modified_count": modified_count
                        })

                        # Save modifications to database with AI tracking
                        try:
                            # Import database function
                            from db_helper import update_test_cases_in_database, DATABASE_PATH
                            import json
                            import time

                            # Get current user
                            current_user = st.session_state.get("admin_username", "anonymous")

                            # Get JIRA ID and test type from current filters or session state
                            current_jira_id = st.session_state.get(get_session_key('filters', 'jira_id'))
                            current_test_type = st.session_state.get(get_session_key('filters', 'dashboard_test_type'))

                            # Prepare AI metadata for database tracking
                            ai_metadata = {
                                'ai_model': selected_model,
                                'user_query': user_query,
                                'original_data': json.dumps(filtered_data.to_dict(orient='records')),
                                'modified_data': json.dumps(modified_df.to_dict(orient='records')),
                                'modification_type': 'content',
                                'processing_time': 0.0,  # We don't track this in the current implementation
                                'tokens_used': 0  # We don't track this in the current implementation
                            }

                            # If we have the necessary information, save to database
                            if current_jira_id and current_test_type and current_test_type != 'All':
                                success, message = update_test_cases_in_database(
                                    DATABASE_PATH,
                                    modified_df,
                                    current_jira_id,
                                    current_test_type,
                                    current_user,
                                    is_edited=True,
                                    ai_metadata=ai_metadata
                                )

                                if success:
                                    st.success(f"💾 {message}")
                                    st.info("🤖 AI modification tracked in database with full metadata")
                                    # Clear cached data to force refresh from database
                                    clear_all_cache_for_fresh_data()
                                else:
                                    st.warning(f"⚠️ Database save failed: {message}")
                                    st.info("💡 Changes are applied in memory but not saved to database")
                            else:
                                st.info("💡 Changes applied in memory. Database save requires specific JIRA ID and test type filters.")
                                st.info("🔍 Set specific JIRA ID and test type filters to enable database persistence")
                                # Store modified data in session state for immediate display
                                st.session_state["ai_modified_data"] = modified_df
                                st.session_state["ai_modification_timestamp"] = datetime.now().isoformat()
                                # Still trigger refresh to show the modified data in the interface
                                st.rerun()

                        except Exception as db_error:
                            st.warning(f"⚠️ Database save error: {str(db_error)}")
                            st.info("💡 Changes are applied in memory but not saved to database")
                            # Store modified data in session state for immediate display
                            st.session_state["ai_modified_data"] = modified_df
                            st.session_state["ai_modification_timestamp"] = datetime.now().isoformat()
                            # Still trigger refresh to show the modified data in the interface
                            st.rerun()

                        # Store modified data in session state for immediate display
                        st.session_state["ai_modified_data"] = modified_df
                        st.session_state["ai_modification_timestamp"] = datetime.now().isoformat()

                        st.info("🔄 Table will automatically refresh to show the updated data.")

                        # Trigger automatic refresh to show the modified data
                        st.rerun()

                        return modified_df

                    else:
                        # Check if error is related to API key
                        if "API_KEY" in error_message or "api_key" in error_message.lower():
                            st.error("🔑 **API Key Configuration Error**")
                            st.error(f"❌ {error_message}")
                            st.info("💡 **Solution**: Ensure your Google API key is properly configured:")
                            st.info("   • Check that the API key exists in your config.json file")
                            st.info("   • Verify the API key has the correct permissions")
                            st.info("   • The same API key that works for test case generation should work here")
                        else:
                            st.error(f"❌ Modification failed: {error_message}")
                        return filtered_data

                except Exception as e:
                    error_str = str(e)
                    if "API_KEY" in error_str or "api_key" in error_str.lower():
                        st.error("🔑 **API Key Configuration Error**")
                        st.error(f"❌ {error_str}")
                        st.info("💡 **Solution**: Ensure your Google API key is properly configured:")
                        st.info("   • Check that the API key exists in your config.json file")
                        st.info("   • Verify the API key has the correct permissions")
                        st.info("   • The same API key that works for test case generation should work here")
                    else:
                        st.error(f"❌ Error during modification: {error_str}")
                    return filtered_data

        # Show modification history if available
        if "unified_modification_history" in st.session_state and st.session_state["unified_modification_history"]:
            st.markdown("---")
            st.markdown("**📜 Recent Modifications**")
            history = st.session_state["unified_modification_history"]
            for i, mod in enumerate(reversed(history[-3:])):  # Show last 3 modifications
                st.markdown(f"**{mod['timestamp']}** ({mod['ai_provider']} - {mod['model']})")
                st.markdown(f"*{mod['query']}*")
                if mod['original_count'] != mod['modified_count']:
                    st.caption(f"Changed from {mod['original_count']} to {mod['modified_count']} test cases")
                if i < len(history) - 1:
                    st.markdown("---")

    return filtered_data

def render_export_section(edited_df: pd.DataFrame, filtered_count: int):
    """
    Render the export section with format selection and download functionality.

    Args:
        edited_df (pd.DataFrame): Edited test case data
        filtered_count (int): Number of filtered test cases
    """
    st.markdown("---")
    st.subheader("📤 Export Filtered Data")

    with st.container():
        col1, col2 = st.columns(2)

        with col1:
            # Export format selection - use a different key to avoid conflicts
            current_export_format = st.selectbox(
                "Export Format:",
                options=["Excel", "CSV"],
                key="unified_current_export_format",
                help="Choose the format for exporting filtered test cases"
            )

            # Prepare export button
            if st.button("📤 Prepare Export", key="unified_prepare_export", use_container_width=True):
                if not edited_df.empty:
                    try:
                        # Generate filename using configuration
                        filename = get_export_filename(current_export_format, include_timestamp=True)

                        # Prepare export data based on format
                        if current_export_format == "Excel":
                            # Use existing Excel export functionality
                            from pathlib import Path
                            temp_dir = Path("Test_cases") / "unified_exports"
                            temp_dir.mkdir(parents=True, exist_ok=True)
                            temp_path = temp_dir / filename

                            # Create Excel file using existing helper
                            create_formatted_excel_from_scenarios(
                                edited_df,
                                str(temp_path),
                                is_dataframe=True,
                                save_to_db=False,
                                test_type="filtered",
                                create_excel=True
                            )

                            # Read the created file for download
                            with open(temp_path, "rb") as f:
                                file_content = f.read()

                        else:  # CSV format
                            # Format DataFrame for CSV export using existing helper
                            csv_df = format_csv_for_external_tools(edited_df)
                            file_content = csv_df.to_csv(index=False).encode('utf-8')

                        # Store export data in session state with proper validation
                        st.session_state[get_session_key('export', 'ready')] = True
                        st.session_state[get_session_key('export', 'filename')] = filename
                        st.session_state[get_session_key('export', 'format')] = current_export_format
                        st.session_state[get_session_key('export', 'data')] = file_content

                        st.success(f"✅ {current_export_format} file prepared: {filename}")
                        st.info(f"📊 Ready to export {filtered_count:,} filtered test cases")

                    except Exception as e:
                        st.error(f"❌ Error preparing {current_export_format} file: {str(e)}")
                        # Clear export state on error
                        st.session_state[get_session_key('export', 'ready')] = False
                        st.session_state[get_session_key('export', 'data')] = None
                else:
                    st.error("❌ No data available to export")

        with col2:
            # Download button
            export_ready = st.session_state.get(get_session_key('export', 'ready'), False)
            stored_format = st.session_state.get(get_session_key('export', 'format'), 'CSV')
            stored_filename = st.session_state.get(get_session_key('export', 'filename'), 'test_cases.csv')
            stored_data = st.session_state.get(get_session_key('export', 'data'))

            if export_ready and not edited_df.empty and stored_data is not None:
                # Get MIME type and button label using configuration
                mime_type = get_mime_type(stored_format)
                button_label = f"📥 Download {stored_format} File"

                st.download_button(
                    label=button_label,
                    data=stored_data,
                    file_name=stored_filename,
                    mime=mime_type,
                    key="unified_download_export",
                    use_container_width=True,
                    help=f"Download {filtered_count:,} filtered test cases as {stored_format}"
                )
            else:
                # Disabled download button with proper default data
                st.download_button(
                    label="📥 Download File",
                    data=b"",  # Empty bytes, not None
                    file_name="test_cases.csv",
                    mime="text/csv",
                    disabled=True,
                    help="Click 'Prepare Export' first to generate the file",
                    key="unified_download_disabled",
                    use_container_width=True
                )

def should_show_unified_interface() -> bool:
    """
    Determine if the unified interface should be displayed.

    Returns:
        bool: True if interface should be shown, False otherwise
    """
    # Check if test cases have been generated in this session
    test_cases_generated = st.session_state.get("test_cases_generated", False)

    # Check if there's scenario data (indicating successful generation)
    has_scenario_data = (
        "scenario_data" in st.session_state and
        st.session_state.scenario_data and
        st.session_state.scenario_data.get("output_file") is not None
    )

    # Check if user is on the generator page
    on_generator_page = st.session_state.get("current_page") == "generator"

    # Check if user is logged in
    is_logged_in = st.session_state.get("is_admin_logged_in", False)

    return test_cases_generated and has_scenario_data and on_generator_page and is_logged_in

def mark_test_cases_generated():
    """
    Mark that test cases have been successfully generated in this session.
    This should be called after successful test case generation.
    """
    st.session_state["test_cases_generated"] = True
    # Reset the interface initialization flag so smart defaults can be applied
    st.session_state["unified_interface_initialized"] = False

    # Track test case ID after generation for range detection
    track_test_case_id_after_generation()

    # Store smart defaults immediately after generation
    store_smart_defaults_from_generation()

def reset_test_case_generation_state():
    """
    Reset the test case generation state.
    This can be called when starting a new generation or resetting the session.
    """
    st.session_state["test_cases_generated"] = False
    st.session_state["unified_interface_initialized"] = False

def load_filter_options() -> Dict:
    """
    Load filter options from database with caching.

    Returns:
        Dict: Available filter options for dropdowns
    """
    # Force refresh of filter options by clearing cache if needed
    # This ensures we always get the latest database values
    try:
        print("🔄 Loading filter options from database...")
        filter_options = get_filter_options(DATABASE_PATH)
        st.session_state.unified_filter_options = filter_options
        print(f"✅ Filter options loaded successfully: {filter_options.get('statistics', {})}")
        return filter_options
    except Exception as e:
        print(f"❌ Error loading filter options: {str(e)}")
        st.error(f"Error loading filter options: {str(e)}")
        # Comprehensive fallback with all expected fields
        fallback_options = {
            'jira_ids': [],
            'test_types': ['POSITIVE', 'NEGATIVE', 'SECURITY', 'PERFORMANCE'],
            'dashboard_test_types': ['positive', 'negative', 'security', 'performance'],
            'priorities': ['High', 'Medium', 'Low'],
            'test_groups': [],
            'features': [],
            'projects': [],
            'users': [],
            'date_range': {'min_date': None, 'max_date': None},
            'statistics': {}
        }
        st.session_state.unified_filter_options = fallback_options
        return fallback_options

def render_unified_test_case_interface(apply_smart_defaults=False):
    """
    Main function to render the complete unified test case interface with manual filter updates.

    Args:
        apply_smart_defaults (bool): If True, apply smart defaults for newly generated test cases

    This function orchestrates all components of the unified interface including
    filters, data table, export functionality, and professional styling.
    Uses manual filter update system where filters only update the table when explicitly applied.
    """
    # Apply custom CSS
    st.markdown(create_unified_interface_css(), unsafe_allow_html=True)
    st.markdown(create_jira_details_css(), unsafe_allow_html=True)

    # Initialize session state with smart defaults if requested
    initialize_unified_session_state(apply_smart_defaults=apply_smart_defaults)

    # Load filter options
    filter_options = load_filter_options()

    # Render filter sections (UI state only - doesn't trigger database queries)
    with st.container():
        render_basic_filters(filter_options)
        render_advanced_filters(filter_options)

    # Render Apply/Reset filter buttons and handle actions
    apply_clicked, reset_clicked = render_filter_action_buttons()

    # Handle filter actions
    if apply_clicked:
        apply_filters_to_database()
        st.rerun()

    if reset_clicked:
        reset_filters_to_smart_defaults()
        st.rerun()

    # Get current applied filters for database query
    current_applied_filters = get_current_applied_filters()

    # Clear export state when applied filters change (to prevent stale export data)
    current_filter_hash = str(sorted(current_applied_filters.items()))
    if 'last_applied_filter_hash' not in st.session_state:
        st.session_state.last_applied_filter_hash = current_filter_hash
    elif st.session_state.last_applied_filter_hash != current_filter_hash:
        clear_export_state()
        st.session_state.last_applied_filter_hash = current_filter_hash

    try:
        # Force fresh database calls every time - no caching
        # Add timestamp to ensure fresh data retrieval
        from datetime import datetime
        refresh_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")

        print(f"🔄 [{refresh_timestamp}] Making fresh database call for test cases table")
        print(f"📊 Applied filters: {current_applied_filters}")

        # Check if we have recent AI-modified data in session state
        ai_modified_data = st.session_state.get("ai_modified_data")
        ai_modification_timestamp = st.session_state.get("ai_modification_timestamp")

        # Use AI-modified data if it's recent (within last 30 seconds) and matches current filters
        use_ai_modified_data = False
        if ai_modified_data is not None and ai_modification_timestamp:
            try:
                mod_time = datetime.fromisoformat(ai_modification_timestamp)
                time_diff = (datetime.now() - mod_time).total_seconds()
                if time_diff < 30:  # Use AI data if modified within last 30 seconds
                    use_ai_modified_data = True
                    print(f"🤖 [{refresh_timestamp}] Using recent AI-modified data from session state")
            except:
                pass

        if use_ai_modified_data:
            # Use AI-modified data for display
            filtered_data = ai_modified_data.copy()
            filtered_unique_count = count_unique_test_cases(filtered_data)
            print(f"✅ [{refresh_timestamp}] Using AI-modified data: {len(filtered_data)} rows ({filtered_unique_count} unique test cases)")

            # Still get total count from database for comparison
            total_data = get_unified_filtered_test_cases(DATABASE_PATH, {})
            total_unique_count = count_unique_test_cases(total_data)
            print(f"📈 [{refresh_timestamp}] Total in database: {len(total_data)} rows ({total_unique_count} unique test cases)")
        else:
            # Fetch filtered data from database using applied filters (always fresh)
            filtered_data = get_unified_filtered_test_cases(DATABASE_PATH, current_applied_filters)
            filtered_unique_count = count_unique_test_cases(filtered_data)
            print(f"✅ [{refresh_timestamp}] Retrieved {len(filtered_data)} filtered rows ({filtered_unique_count} unique test cases)")

            # Get total count for comparison (fetch without filters, always fresh)
            total_data = get_unified_filtered_test_cases(DATABASE_PATH, {})
            total_unique_count = count_unique_test_cases(total_data)
            print(f"📈 [{refresh_timestamp}] Total in database: {len(total_data)} rows ({total_unique_count} unique test cases)")

    except Exception as e:
        st.error(f"Error loading test case data: {str(e)}")
        filtered_data = pd.DataFrame()
        total_data = pd.DataFrame()

    # Render count indicator with refresh button (now uses DataFrames for accurate counting)
    render_count_indicator_with_refresh(filtered_data, total_data, refresh_timestamp)

    # Render data table
    with st.container():
        edited_df = render_data_table(filtered_data)

    # Render AI modification section
    if not filtered_data.empty:
        with st.container():
            modified_data = render_ai_modification_section(filtered_data)
            # If modifications were applied, use the modified data for export
            if not modified_data.equals(filtered_data):
                export_data = modified_data
            else:
                export_data = edited_df
    else:
        export_data = edited_df

    # Render export section
    if not export_data.empty:
        with st.container():
            # Use unique test case count for export
            filtered_unique_count = count_unique_test_cases(export_data)
            render_export_section(export_data, filtered_unique_count)

# Export the main functions for use in other modules
__all__ = [
    'render_unified_test_case_interface',
    'should_show_unified_interface',
    'mark_test_cases_generated',
    'reset_test_case_generation_state',
    'initialize_unified_session_state',
    'get_smart_default_value',
    'get_current_generation_run_info',
    'track_test_case_id_before_generation',
    'track_test_case_id_after_generation',
    'get_current_generation_test_case_id_range',
    'clear_all_cache_for_fresh_data',
    'has_valid_test_case_id_range_data',
    'ensure_test_case_id_range_filtering_applied',
    'render_ai_modification_section'
]