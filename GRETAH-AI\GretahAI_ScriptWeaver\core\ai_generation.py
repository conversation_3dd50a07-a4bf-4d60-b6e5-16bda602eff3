"""
AI-powered test script generation functionality.

This module handles the generation of test scripts from test cases using AI,
including test data generation and comprehensive script creation.
"""

import json
from typing import Dict, Any, Optional

# Import debug utility
from debug_utils import debug


def generate_test_data_prompt(step_data, ui_elements, step_table_entry=None):
    """
    Generate a prompt for test data generation.

    Args:
        step_data (dict): Test step data with action and expected result
        ui_elements (list): List of UI elements relevant to the test step
        step_table_entry (dict, optional): Step table entry with structured data

    Returns:
        str: The prompt for test data generation
    """
    debug("=== ENTERING generate_test_data_prompt ===",
          stage="ai_generation", operation="generate_test_data_prompt")
    debug(f"Input step_data: {step_data}",
          stage="ai_generation", operation="generate_test_data_prompt",
          context={'step_data': step_data})
    debug(f"Input ui_elements: {type(ui_elements)} with {len(ui_elements) if isinstance(ui_elements, list) else 'N/A'} elements",
          stage="ai_generation", operation="generate_test_data_prompt",
          context={'ui_elements_type': type(ui_elements).__name__, 'ui_elements_count': len(ui_elements) if isinstance(ui_elements, list) else 'N/A'})
    debug(f"Input step_table_entry: {step_table_entry}",
          stage="ai_generation", operation="generate_test_data_prompt",
          context={'step_table_entry': step_table_entry})

    try:
        # Prepare the prompt for AI
        debug("Preparing prompt for AI",
              stage="ai_generation", operation="generate_test_data_prompt")
        step_table_info = ""
        if step_table_entry and isinstance(step_table_entry, dict):
            debug("Building step table info for prompt",
                  stage="ai_generation", operation="generate_test_data_prompt")
            step_table_info = f"""
            Step Table Information:
            - Step Type: {step_table_entry.get('step_type', 'N/A')}
            - Action: {step_table_entry.get('action', 'N/A')}
            - Locator Strategy: {step_table_entry.get('locator_strategy', 'N/A')}
            - Locator: {step_table_entry.get('locator', 'N/A')}
            - Test Data Parameter: {step_table_entry.get('test_data_param', 'N/A')}
            - Expected Result: {step_table_entry.get('expected_result', 'N/A')}
            - Assertion Type: {step_table_entry.get('assertion_type', 'N/A')}
            - Description: {step_table_entry.get('step_description', 'N/A')}
            """
            debug("Successfully built step table info",
                  stage="ai_generation", operation="generate_test_data_prompt")
        else:
            debug("No step table entry provided or it's not a dictionary",
                  stage="ai_generation", operation="generate_test_data_prompt")

        debug("Building main prompt",
              stage="ai_generation", operation="generate_test_data_prompt")
        # Safely serialize UI elements to JSON
        try:
            ui_elements_json = json.dumps(ui_elements, indent=2)
            debug(f"Successfully serialized UI elements to JSON ({len(ui_elements_json)} chars)",
                  stage="ai_generation", operation="generate_test_data_prompt",
                  context={'ui_elements_json_length': len(ui_elements_json)})
        except Exception as json_err:
            debug(f"Error serializing UI elements to JSON: {json_err}",
                  stage="ai_generation", operation="generate_test_data_prompt",
                  context={'error': str(json_err), 'error_type': type(json_err).__name__})
            # Create a simplified version that can be serialized
            simplified_elements = []
            if isinstance(ui_elements, list):
                for elem in ui_elements:
                    if isinstance(elem, dict):
                        simplified_elem = {
                            "name": elem.get("name", "Unknown"),
                            "type": elem.get("type", "Unknown"),
                            "attributes": {
                                "id": elem.get("attributes", {}).get("id", ""),
                                "tag": elem.get("attributes", {}).get("tag", "")
                            }
                        }
                        simplified_elements.append(simplified_elem)
            ui_elements_json = json.dumps(simplified_elements, indent=2)
            debug(f"Created simplified UI elements JSON ({len(ui_elements_json)} chars)",
                  stage="ai_generation", operation="generate_test_data_prompt",
                  context={'simplified_ui_elements_json_length': len(ui_elements_json)})

        prompt = f"""
        I need to generate realistic and contextually appropriate test data for the following test step:
        - Action: {step_data.get('action', '')}
        - Expected Result: {step_data.get('expected', '')}

        {step_table_info}

        The UI elements involved in this step are:
        {ui_elements_json}

        Please generate high-quality test data for this step as a JSON object with key-value pairs.
        Each key should be a variable name for the test data, and each value should be the test data value.

        IMPORTANT GUIDELINES:
        1. Generate REALISTIC data that would be used in a real-world scenario, not generic placeholders
        2. If the step involves form submission, include ALL required fields for that form
        3. If the step involves validation testing, include both valid and invalid test data
        4. For text fields, use realistic content (not "test_fieldname")
        5. For emails, use realistic formats like "<EMAIL>"
        6. For names, use realistic full names like "John Smith" or "Maria Rodriguez"
        7. For addresses, use realistic street addresses, cities, states, and zip codes
        8. For credit cards, use valid test card numbers (e.g., "****************" for Visa)
        9. For dates, use proper date formats appropriate to the context
        10. If the step table has test data parameters in {{{{param}}}} format, prioritize generating values for these parameters

        Example of high-quality test data:
        {{
          "email": "<EMAIL>",
          "password": "SecurePass123!",
          "first_name": "John",
          "last_name": "Smith",
          "address": "123 Main Street",
          "city": "Boston",
          "state": "MA",
          "zip_code": "02108",
          "credit_card": "****************",
          "expiry_date": "12/25",
          "cvv": "123"
        }}

        Return ONLY the JSON object with no additional text or explanation.
        """
        debug("Successfully built main prompt",
              stage="ai_generation", operation="generate_test_data_prompt")
        debug(f"Prompt length: {len(prompt)} characters",
              stage="ai_generation", operation="generate_test_data_prompt",
              context={'prompt_length': len(prompt)})

        # Log a truncated version of the prompt for debugging
        if len(prompt) > 500:
            debug(f"Prompt (first 200 chars): {prompt[:200]}...",
                  stage="ai_generation", operation="generate_test_data_prompt",
                  context={'prompt_preview': 'truncated'})
            debug(f"Prompt (last 200 chars): ...{prompt[-200:]}",
                  stage="ai_generation", operation="generate_test_data_prompt",
                  context={'prompt_preview': 'truncated'})
        else:
            debug(f"Full prompt: {prompt}",
                  stage="ai_generation", operation="generate_test_data_prompt",
                  context={'prompt_preview': 'full'})

        debug("=== EXITING generate_test_data_prompt ===",
              stage="ai_generation", operation="generate_test_data_prompt")
        return prompt
    except Exception as e:
        debug(f"Error generating test data prompt: {e}",
              stage="ai_generation", operation="generate_test_data_prompt",
              context={'error': str(e), 'error_type': type(e).__name__})
        debug(f"Exception details: {str(e)}",
              stage="ai_generation", operation="generate_test_data_prompt",
              context={'exception_details': str(e)})
        debug(f"Exception type: {type(e)}",
              stage="ai_generation", operation="generate_test_data_prompt",
              context={'exception_type': type(e).__name__})
        import traceback
        debug(f"Traceback: {traceback.format_exc()}",
              stage="ai_generation", operation="generate_test_data_prompt",
              context={'traceback': traceback.format_exc()})
        return f"Error: {str(e)}"


def generate_test_cases_data_prompt(test_cases):
    """
    Generate a prompt for test data dictionary generation based on multiple test cases.

    Args:
        test_cases (list): List of test cases

    Returns:
        str: The prompt for test data dictionary generation
    """
    try:
        # Initialize test data with common defaults
        test_data = {
            # Login data
            "valid_email": "<EMAIL>",
            "invalid_email": "invalid.email",
            "valid_password": "Password123!",
            "invalid_password": "pass",

            # User data
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "phone_number": "1234567890",
            "mobile_number": "9876543210",

            # Payment data
            "card_number": "****************",
            "card_expiry": "12/25",
            "card_cvv": "123",
            "card_holder": "John Doe"
        }

        # Prepare the prompt for AI
        test_cases_summary = []
        for tc in test_cases[:5]:  # Limit to first 5 test cases to avoid token limits
            if isinstance(tc, dict):
                tc_id = tc.get('Test Case ID', '')
                objective = tc.get('Test Case Objective', '')
                steps = []
                for step in tc.get('Steps', []):
                    if isinstance(step, dict):
                        steps.append({
                            "action": step.get('Test Steps', ''),
                            "expected": step.get('Expected Result', '')
                        })
                test_cases_summary.append({
                    "id": tc_id,
                    "objective": objective,
                    "steps": steps[:3]  # Limit to first 3 steps
                })

        prompt = f"""
        I need to generate test data for the following test cases:
        {json.dumps(test_cases_summary, indent=2)}

        Please enhance this existing test data with more appropriate values based on the test cases:
        {json.dumps(test_data, indent=2)}

        Return only a JSON object with the enhanced test data. Keep the same keys but improve the values.
        """

        return prompt
    except Exception as e:
        debug(f"Error generating test cases data prompt: {e}",
              stage="ai_generation", operation="generate_test_cases_data_prompt",
              context={'error': str(e), 'error_type': type(e).__name__})
        return f"Error: {str(e)}"


def generate_test_script(test_case, step_matches, test_data, website_url, step_table_entry=None, api_key=None, state=None, custom_instructions=None, prompt_context=None):
    """
    Generate a test script for a test case using AI.

    Implements a two-phase script generation process:

    Phase 1 - Step-Specific Script Generation:
    - Generates a script that focuses exclusively on the current test step
    - Does not attempt to merge with previous steps or maintain continuity
    - Produces a clean, isolated script that implements only the current step's functionality
    - Useful for debugging and understanding the specific implementation of the current step

    Phase 2 - Script Merging:
    - Takes the step-specific script from Phase 1
    - Intelligently combines it with previously generated scripts from earlier steps
    - Uses the merge_scripts_with_ai function to maintain proper continuity
    - Handles import deduplication, variable reuse, and proper sequencing
    - Creates a cohesive script that implements all steps up to and including the current one

    This modular approach:
    - Makes debugging easier by allowing inspection of both isolated and merged scripts
    - Creates a clearer separation of concerns in the code
    - Allows for potential future enhancements to either generation or merging processes

    Args:
        test_case (dict or list): The test case(s) to generate a script for
        step_matches (dict): Dictionary of element matches for test steps
        test_data (dict): Dictionary of test data values
        website_url (str): The URL of the website to test
        step_table_entry (dict, optional): The step table entry for the step
        api_key (str, optional): API key for Google AI. If None, use initialized client
        state (StateManager, optional): The application state manager instance for script continuity
        custom_instructions (str, optional): Custom instructions for script generation regeneration

    Returns:
        tuple: (merged_script, step_specific_script) where:
            - merged_script (str): The final merged script combining all steps
            - step_specific_script (str): The script for just the current step
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction
    from .ai_merging import merge_scripts_with_ai
    from .prompt_builder import generate_enhanced_test_script_prompt

    try:
        # DEBUG: Log function entry with key parameters for Step 2 debugging
        debug("=== DEBUG: generate_test_script ENTRY ===",
              stage="ai_generation", operation="generate_test_script")
        if step_table_entry:
            step_no = step_table_entry.get('step_no')
            debug(f"Processing Step {step_no}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'step_no': step_no})
            debug(f"Step Table Entry: {step_table_entry}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'step_table_entry': step_table_entry})

        # DEBUG: Log state information for debugging state transitions
        if state:
            debug("=== DEBUG: State Information ===",
                  stage="ai_generation", operation="generate_test_script")
            # Log key state attributes that might affect script generation
            if hasattr(state, 'current_step_index'):
                debug(f"State current_step_index: {state.current_step_index}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'current_step_index': state.current_step_index})
            if hasattr(state, 'browser_initialized'):
                debug(f"State browser_initialized: {state.browser_initialized}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'browser_initialized': state.browser_initialized})
            if hasattr(state, 'previous_scripts'):
                debug(f"State has previous_scripts: {list(state.previous_scripts.keys()) if state.previous_scripts else 'None'}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'previous_scripts_keys': list(state.previous_scripts.keys()) if state.previous_scripts else None})
            if hasattr(state, 'script_imports'):
                debug(f"State has script_imports: {len(state.script_imports) if state.script_imports else 0} imports",
                      stage="ai_generation", operation="generate_test_script",
                      context={'script_imports_count': len(state.script_imports) if state.script_imports else 0})
            if hasattr(state, 'script_variables'):
                debug(f"State has script_variables: {list(state.script_variables.keys()) if state.script_variables else 'None'}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'script_variables_keys': list(state.script_variables.keys()) if state.script_variables else None})

        # Create a set to track seen imports, fixtures, and helper functions
        seen_imports = set()

        # Handle case where test_case is a list (take the first item)
        if isinstance(test_case, list):
            if not test_case:
                return "Error: Empty test case list provided.", ""
            test_case = test_case[0]

        # Handle case where step_matches is a list (convert to expected dictionary format)
        if isinstance(step_matches, list):
            debug(f"In generate_test_script: step_matches is a list with {len(step_matches)} elements",
                  stage="ai_generation", operation="generate_test_script",
                  context={'step_matches_type': 'list', 'step_matches_count': len(step_matches)})
            # Convert list to a dictionary format expected by the rest of the function
            if test_case and step_table_entry:
                tc_id = test_case.get('Test Case ID', 'unknown')
                step_no = step_table_entry.get('step_no', '1')

                # Create a properly structured dictionary
                step_matches = {
                    tc_id: {
                        step_no: step_matches
                    }
                }
                debug(f"Converted step_matches list to dictionary with TC ID: {tc_id}, Step: {step_no}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'tc_id': tc_id, 'step_no': step_no, 'conversion': 'list_to_dict'})
            else:
                debug("Cannot convert step_matches list to dictionary - missing test_case or step_table_entry",
                      stage="ai_generation", operation="generate_test_script",
                      context={'conversion_error': 'missing_parameters'})
                # Create an empty dictionary as fallback
                step_matches = {}

        # Log enhanced prompt generation process
        debug("=" * 60,
              stage="ai_generation", operation="generate_test_script")
        debug("ENHANCED PROMPT GENERATION WITH FEEDBACK LOOP",
              stage="ai_generation", operation="generate_test_script")
        debug("=" * 60,
              stage="ai_generation", operation="generate_test_script")

        # Check if state has feedback history
        has_feedback = hasattr(state, 'validation_feedback_history') and state.validation_feedback_history if state else False
        feedback_count = len(state.validation_feedback_history) if has_feedback else 0
        regeneration_count = getattr(state, 'script_regeneration_count', 0) if state else 0

        debug(f"State object available: {state is not None}",
              stage="ai_generation", operation="generate_test_script",
              context={'state_available': state is not None})
        debug(f"Validation feedback history: {feedback_count} entries",
              stage="ai_generation", operation="generate_test_script",
              context={'feedback_count': feedback_count})
        debug(f"Script regeneration count: {regeneration_count}",
              stage="ai_generation", operation="generate_test_script",
              context={'regeneration_count': regeneration_count})

        # Get common issues for logging (simplified without deprecated analytics)
        common_issues = []  # Simplified - removed deprecated analytics method
        debug("No common validation issues available (analytics method removed)",
              stage="ai_generation", operation="generate_test_script")

        # Generate the enhanced prompt with validation feedback and unified context
        debug("Calling generate_enhanced_test_script_prompt with feedback integration and unified context",
              stage="ai_generation", operation="generate_test_script")

        # Use unified prompt context if available, otherwise fall back to individual parameters
        if prompt_context:
            debug("Using unified prompt context for script generation",
                  stage="ai_generation", operation="generate_test_script",
                  context={'has_ai_enhanced_context': bool(prompt_context.get('ai_enhanced_context')),
                          'has_user_overrides': bool(prompt_context.get('user_overrides'))})

            # Extract enhanced custom instructions from unified context
            enhanced_custom_instructions = custom_instructions
            if prompt_context.get('ai_enhanced_context'):
                if enhanced_custom_instructions:
                    enhanced_custom_instructions = f"{enhanced_custom_instructions}\n\n**AI-Enhanced Context:**\n{prompt_context['ai_enhanced_context']}"
                else:
                    enhanced_custom_instructions = prompt_context['ai_enhanced_context']

            prompt = generate_enhanced_test_script_prompt(
                test_case=test_case,
                step_matches=step_matches,
                test_data=test_data,
                website_url=website_url,
                step_table_entry=step_table_entry,
                state=state,
                include_validation_feedback=True,
                custom_instructions=enhanced_custom_instructions
            )
        else:
            debug("Using legacy prompt generation (no unified context)",
                  stage="ai_generation", operation="generate_test_script")
            prompt = generate_enhanced_test_script_prompt(
                test_case=test_case,
                step_matches=step_matches,
                test_data=test_data,
                website_url=website_url,
                step_table_entry=step_table_entry,
                state=state,
                include_validation_feedback=True,
                custom_instructions=custom_instructions
            )

        # Log prompt generation results
        prompt_length = len(prompt)
        has_feedback_summary = 'Recent Feedback Summary' in prompt
        has_specific_improvements = 'Specific Improvements' in prompt
        has_validation_guidelines = 'Validation Guidelines' in prompt

        debug(f"Enhanced prompt generated successfully:",
              stage="ai_generation", operation="generate_test_script")
        debug(f"  - Total length: {prompt_length} characters",
              stage="ai_generation", operation="generate_test_script",
              context={'prompt_length': prompt_length})
        debug(f"  - Contains feedback summary: {has_feedback_summary}",
              stage="ai_generation", operation="generate_test_script",
              context={'has_feedback_summary': has_feedback_summary})
        debug(f"  - Contains specific improvements: {has_specific_improvements}",
              stage="ai_generation", operation="generate_test_script",
              context={'has_specific_improvements': has_specific_improvements})
        debug(f"  - Contains validation guidelines: {has_validation_guidelines}",
              stage="ai_generation", operation="generate_test_script",
              context={'has_validation_guidelines': has_validation_guidelines})

        if regeneration_count > 0:
            debug(f"✅ REGENERATION CONFIRMED: This is regeneration attempt #{regeneration_count}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'regeneration_attempt': regeneration_count})
            debug(f"✅ FEEDBACK LOOP ACTIVE: {len(common_issues)} guidelines included in prompt",
                  stage="ai_generation", operation="generate_test_script",
                  context={'feedback_guidelines_count': len(common_issues)})
        else:
            debug("ℹ️ Initial generation (no previous feedback)",
                  stage="ai_generation", operation="generate_test_script")
            debug("=" * 60,
                  stage="ai_generation", operation="generate_test_script")

        # DEBUG: Log the prompt structure (truncated for readability)
        if step_table_entry and step_table_entry.get('step_no') == '2':
            debug("=== DEBUG: Prompt for Step 2 ===",
                  stage="ai_generation", operation="generate_test_script")
            # Log key sections of the prompt
            prompt_lines = prompt.split('\n')
            context_section = []
            element_matches_section = []
            test_data_section = []

            in_element_matches = False
            in_test_data = False

            for line in prompt_lines:
                if "### ELEMENT_MATCHES (JSON)" in line:
                    in_element_matches = True
                    in_test_data = False
                    continue
                elif "### TEST_DATA (JSON)" in line:
                    in_element_matches = False
                    in_test_data = True
                    continue
                elif "### PREVIOUS-STEP SUMMARY" in line:
                    in_element_matches = False
                    in_test_data = False
                    continue

                if in_element_matches:
                    element_matches_section.append(line)
                elif in_test_data:
                    test_data_section.append(line)
                elif "CONTEXT" in line or "Test-Case ID" in line or "Objective" in line or "Current Step No." in line or "Action" in line or "Expected" in line:
                    context_section.append(line)

            debug(f"Context Section: {context_section}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'context_section': context_section})
            debug(f"Element Matches Section (first 5 lines): {element_matches_section[:5]}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'element_matches_preview': element_matches_section[:5]})
            debug(f"Test Data Section (first 5 lines): {test_data_section[:5]}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'test_data_preview': test_data_section[:5]})

        # =====================================================================
        # PHASE 1: Generate a step-specific script that focuses only on the current test step
        # =====================================================================
        # This phase generates a script that implements only the current step's functionality,
        # without any attempt to merge with previous steps or maintain continuity.
        # The resulting script is clean, isolated, and focused on a single task.
        debug("PHASE 1: Generating step-specific script",
              stage="ai_generation", operation="generate_test_script")

        # Log the prompt for script generation separately with enhanced prompt generation tracing
        prompt_log_filepath, prompt_request_id = log_ai_interaction(
            function_name="generate_test_script_prompt",
            prompt=prompt,
            response="See generate_llm_response log for the response",
            model_name="gemini-1.5-flash",
            capture_stack=True,
            is_prompt_generation=True,  # Enable prompt generation tracing
            context={
                'test_case_id': test_case.get('Test Case ID', 'unknown'),
                'step_no': step_table_entry.get('step_no') if step_table_entry else 'unknown',
                'action': step_table_entry.get('action') if step_table_entry else 'unknown',
                'website_url': website_url,
                'has_step_matches': bool(step_matches),
                'has_test_data': bool(test_data),
                'has_state': bool(state)
            }
        )

        # Generate the script with enhanced logging, linking to the prompt log
        step_specific_script = generate_llm_response(
            prompt=prompt,
            api_key=api_key,
            category="script_generation",
            related_request_ids=[prompt_request_id],  # Link to the prompt log
            context={
                'test_case_id': test_case.get('Test Case ID', 'unknown'),
                'step_no': step_table_entry.get('step_no') if step_table_entry else 'unknown',
                'action': step_table_entry.get('action') if step_table_entry else 'unknown',
                'prompt_log_filepath': prompt_log_filepath,
                'prompt_request_id': prompt_request_id
            }
        )

        # Clean up the response to extract code
        if "```python" in step_specific_script:
            code_start = step_specific_script.find("```python") + 10
            code_end = step_specific_script.rfind("```")
            step_specific_script = step_specific_script[code_start:code_end].strip()
        elif "```" in step_specific_script:
            code_start = step_specific_script.find("```") + 3
            code_end = step_specific_script.rfind("```")
            step_specific_script = step_specific_script[code_start:code_end].strip()

        # Process the script to remove duplicate imports
        seen_imports = set()
        if step_specific_script:
            # Split the script into lines
            script_lines = step_specific_script.split('\n')
            processed_lines = []

            # Process each line
            for line in script_lines:
                # Check if the line is an import statement
                if line.strip().startswith(('import ', 'from ')):
                    if line.strip() not in seen_imports:
                        seen_imports.add(line.strip())
                        processed_lines.append(line)
                # Check if the line is a fixture or helper function definition
                elif line.strip().startswith(('@pytest.fixture', 'def ')):
                    # For function definitions, we need to check the entire function
                    # This is a simplification - in a real implementation, you'd need to
                    # track indentation to identify the full function
                    if line.strip() not in seen_imports:
                        seen_imports.add(line.strip())
                        processed_lines.append(line)
                else:
                    processed_lines.append(line)

            # Reassemble the script
            step_specific_script = '\n'.join(processed_lines)

            # Add WebDriverManager log level setting after the first import os statement
            if 'import os' in step_specific_script and 'os.environ["WDM_LOG_LEVEL"] = "0"' not in step_specific_script:
                step_specific_script = step_specific_script.replace(
                    'import os',
                    'import os\nos.environ["WDM_LOG_LEVEL"] = "0"'
                )

        # Post-process the code to ensure it only implements the selected step
        # Find the current step based on step_table_entry
        current_step = None
        if step_table_entry:
            step_no = step_table_entry.get('step_no')
            if step_no:
                for step in test_case.get('Steps', []):
                    if str(step.get('Step No')) == str(step_no):
                        current_step = step
                        break

                # DEBUG: Log if we found the step or not for post-processing
                if current_step:
                    debug(f"Found matching step for post-processing: Step {step_no} - {current_step.get('Test Steps')}",
                          stage="ai_generation", operation="generate_test_script",
                          context={'step_no': step_no, 'step_found': True, 'step_action': current_step.get('Test Steps')})
                else:
                    debug(f"Could not find matching step {step_no} for post-processing",
                          stage="ai_generation", operation="generate_test_script",
                          context={'step_no': step_no, 'step_found': False})

        # If we couldn't find the step, use the first step as fallback
        if not current_step and test_case.get('Steps'):
            steps = test_case.get('Steps')
            if steps and len(steps) > 0:
                debug("Could not find matching step for post-processing, using first step as fallback",
                      stage="ai_generation", operation="generate_test_script",
                      context={'fallback_used': True})
                current_step = steps[0]
                debug(f"Using fallback step for post-processing: {current_step.get('Test Steps')}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'fallback_step_action': current_step.get('Test Steps')})
            else:
                debug("Test case has empty Steps list, cannot use fallback step for post-processing",
                      stage="ai_generation", operation="generate_test_script",
                      context={'steps_empty': True})

        # Check if this is a navigation step
        step_action = current_step.get('Test Steps', '').lower() if current_step else ''
        is_navigation_step = any(nav in step_action for nav in ["navigate", "go to", "open", "visit", "browse to"])

        # DEBUG: Log step type identification
        debug(f"=== DEBUG: Step Type Identification ===",
              stage="ai_generation", operation="generate_test_script")
        debug(f"Step Action: '{step_action}'",
              stage="ai_generation", operation="generate_test_script",
              context={'step_action': step_action})
        debug(f"Is Navigation Step: {is_navigation_step}",
              stage="ai_generation", operation="generate_test_script",
              context={'is_navigation_step': is_navigation_step})

        # DEBUG: For Step 2 specifically, log more details about the step
        if step_table_entry and step_table_entry.get('step_no') == '2':
            debug(f"=== DEBUG: Step 2 Details ===",
                  stage="ai_generation", operation="generate_test_script")
            debug(f"Full Step Info: {current_step}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'step_2_info': current_step})
            if step_table_entry:
                debug(f"Step Table Entry Action: {step_table_entry.get('action')}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'step_table_action': step_table_entry.get('action')})
                debug(f"Step Table Entry Locator Strategy: {step_table_entry.get('locator_strategy')}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'locator_strategy': step_table_entry.get('locator_strategy')})
                debug(f"Step Table Entry Element: {step_table_entry.get('element')}",
                      stage="ai_generation", operation="generate_test_script",
                      context={'element': step_table_entry.get('element')})

            # Log the first 10 lines of the generated script for Step 2
            if step_specific_script:
                script_lines = step_specific_script.split('\n')
                debug(f"First 10 lines of Step 2 script before post-processing:",
                      stage="ai_generation", operation="generate_test_script",
                      context={'script_lines_count': len(script_lines)})
                for i, line in enumerate(script_lines[:10]):
                    debug(f"  Line {i+1}: {line}",
                          stage="ai_generation", operation="generate_test_script",
                          context={'line_number': i+1, 'line_content': line})

        # For navigation steps, filter out any code that checks for UI elements
        if is_navigation_step:
            # List of patterns that should not be in navigation step code
            forbidden_patterns = [
                "find_element",
                "visibility_of_element_located",
                "presence_of_element_located",
                "element_to_be_clickable",
                "By.ID",
                "By.CSS_SELECTOR",
                "By.XPATH",
                "By.CLASS_NAME",
                "By.NAME",
                "By.TAG_NAME",
                "By.LINK_TEXT",
                "By.PARTIAL_LINK_TEXT"
            ]

            # Check if the expected result explicitly mentions title verification
            expected_result = current_step.get('Expected Result', '').lower() if current_step else ''
            title_verification_required = "title" in expected_result

            # DEBUG: Log expected result and title verification requirement
            debug(f"Expected Result: '{expected_result}'",
                  stage="ai_generation", operation="generate_test_script",
                  context={'expected_result': expected_result})
            debug(f"Title Verification Required: {title_verification_required}",
                  stage="ai_generation", operation="generate_test_script",
                  context={'title_verification_required': title_verification_required})

            # If title verification is not required, add title-related patterns to forbidden list
            if not title_verification_required:
                # Use simple string patterns without regex special characters
                forbidden_patterns.extend(["driver.title", "assert", "in driver.title"])
                debug("Added title-related patterns to forbidden list",
                      stage="ai_generation", operation="generate_test_script")

            # Split the code into lines for processing
            code_lines = step_specific_script.split('\n')
            filtered_lines = []
            removed_lines = []

            # Process each line
            for line in code_lines:
                # Skip lines with forbidden patterns unless they're in comments
                if any(pattern in line for pattern in forbidden_patterns) and not line.strip().startswith('#'):
                    # Replace with a comment explaining why it was removed
                    filtered_lines.append(f"# Removed: {line.strip()} - Not needed for navigation step")
                    removed_lines.append(line.strip())
                else:
                    filtered_lines.append(line)

            # DEBUG: Log removed lines
            if removed_lines:
                debug(f"Removed {len(removed_lines)} lines from navigation step script:",
                      stage="ai_generation", operation="generate_test_script",
                      context={'removed_lines_count': len(removed_lines)})
                for line in removed_lines[:5]:  # Log first 5 removed lines
                    debug(f"  Removed: {line}",
                          stage="ai_generation", operation="generate_test_script",
                          context={'removed_line': line})
                if len(removed_lines) > 5:
                    debug(f"  ... and {len(removed_lines) - 5} more lines",
                          stage="ai_generation", operation="generate_test_script",
                          context={'additional_removed_lines': len(removed_lines) - 5})

            # Reassemble the code
            step_specific_script = '\n'.join(filtered_lines)

        # --- Replace placeholder URLs with actual website_url ---
        if step_specific_script:
            import re
            # Fix for URL placeholders with curly braces
            step_specific_script = step_specific_script.replace("{website_url}", website_url)
            step_specific_script = step_specific_script.replace("{{website_url}}", website_url)
            step_specific_script = step_specific_script.replace("{{{website_url}}}", website_url)

            # Fix for URLs that already have curly braces around them
            # Pattern for url = "{url}" - using string concatenation to avoid escape issues
            url_pattern = r'url\s*=\s*[\'"]\{' + '([^}]+)' + r'\}[\'"]\s*'
            step_specific_script = re.sub(url_pattern, r'url = "\1"', step_specific_script)

            # Pattern for EC.url_to_be("{url}") - using string concatenation to avoid escape issues
            url_to_be_pattern = r'EC\.url_to_be\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(url_to_be_pattern, r'EC.url_to_be("\1")', step_specific_script)

            # Pattern for EC.url_contains("{url}") - using string concatenation to avoid escape issues
            url_contains_pattern = r'EC\.url_contains\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(url_contains_pattern, r'EC.url_contains("\1")', step_specific_script)

            # Pattern for EC.url_matches("{url}") - using string concatenation to avoid escape issues
            url_matches_pattern = r'EC\.url_matches\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(url_matches_pattern, r'EC.url_matches("\1")', step_specific_script)

            # Pattern for driver.get("{url}") - using string concatenation to avoid escape issues
            driver_get_pattern = r'driver\.get\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'
            step_specific_script = re.sub(driver_get_pattern, r'driver.get("\1")', step_specific_script)

            # General pattern to catch any remaining URL-related methods with curly braces
            # Using string concatenation to avoid escape issues
            general_url_pattern = r'(\w+\.\w+)\([\'"]\{' + '([^}]+)' + r'\}[\'"]\)'

            # Function to process matches for the general pattern
            def replace_url_in_method(match):
                method = match.group(1)
                url = match.group(2)
                return f'{method}("{url}")'

            # Apply the general pattern replacement
            step_specific_script = re.sub(general_url_pattern, replace_url_in_method, step_specific_script)

            # Special handling for navigation steps with test data parameters
            # Check if we have a step table entry with a test_data_param for a navigation step
            if step_table_entry:
                # Handle navigation steps
                if step_table_entry.get('action', '').lower() in ['navigate', 'go to', 'open', 'visit']:
                    # Get expected result
                    expected_result = step_table_entry.get('expected_result', '')

                    # Fix incorrect title assertions for navigation steps
                    if 'login' in expected_result.lower() and 'title' not in expected_result.lower():
                        # Remove any assertions about page title containing "Login"
                        # Using a safer pattern construction to avoid escape issues
                        # Break the pattern into parts to avoid escape sequence issues
                        title_pattern_parts = [
                            r'assert\s+[\'"]Login[\'"]',
                            r'assert\s+[\'"]login[\'"]'
                        ]
                        for pattern in title_pattern_parts:
                            pattern_with_title = pattern + r'\s+in\s+driver\.title'
                            step_specific_script = re.sub(pattern_with_title,
                                                        '# Navigation step - verifying URL is sufficient',
                                                        step_specific_script)

                    # If there's a test_data_param, handle URL parameters
                    if step_table_entry.get('test_data_param'):
                        # Extract the parameter name without curly braces
                        param_name = step_table_entry.get('test_data_param', '')
                        param_name = param_name.strip('{}')

                        # Look for patterns like url = "{param_name}" or url = "{{param_name}}"
                        # Use raw strings with properly escaped curly braces
                        param_patterns = [
                            r'url\s*=\s*[\'"]\{' + param_name + r'\}[\'"]',
                            r'url\s*=\s*[\'"]{{' + param_name + r'}}[\'"]'
                        ]

                        # If the website URL is a placeholder, replace it with the actual URL
                        if param_name.lower() in ['login_page_url', 'website_url', 'application_url', 'base_url']:
                            for pattern in param_patterns:
                                step_specific_script = re.sub(pattern, f'url = "{website_url}"', step_specific_script)

            # Also replace any legacy placeholders for robustness
            for placeholder in ["YOUR_LOGIN_URL_HERE", "YOUR_APPLICATION_URL", "URL_HERE"]:
                if placeholder in step_specific_script:
                    step_specific_script = step_specific_script.replace(placeholder, website_url)

        # =====================================================================
        # PHASE 2: Merge with previous scripts to maintain continuity
        # =====================================================================
        # This phase takes the step-specific script from Phase 1 and intelligently
        # combines it with previously generated scripts from earlier steps.
        # The merge process handles:
        # - Import deduplication
        # - Variable reuse
        # - Proper sequencing of actions
        # - Maintaining browser state between steps
        # - Ensuring test flow continuity
        debug("PHASE 2: Merging with previous scripts to maintain continuity",
              stage="ai_generation", operation="generate_test_script")

        # Start with the step-specific script as our merged script
        # (If no previous scripts exist, the step-specific script becomes the merged script)
        merged_script = step_specific_script

        # DEBUG: For Step 2, log the step-specific script before merging
        if step_table_entry and step_table_entry.get('step_no') == '2':
            debug("=== DEBUG: Step 2 Script Before Merging ===",
                  stage="ai_generation", operation="generate_test_script")
            if step_specific_script:
                # Log a summary of the script (first 10 lines and last 5 lines)
                script_lines = step_specific_script.split('\n')
                debug(f"Step 2 script length: {len(script_lines)} lines",
                      stage="ai_generation", operation="generate_test_script",
                      context={'step_2_script_lines': len(script_lines)})
                debug("First 10 lines:",
                      stage="ai_generation", operation="generate_test_script")
                for i, line in enumerate(script_lines[:10]):
                    debug(f"  {i+1}: {line}",
                          stage="ai_generation", operation="generate_test_script",
                          context={'line_number': i+1, 'line_content': line})
                if len(script_lines) > 15:
                    debug("Last 5 lines:",
                          stage="ai_generation", operation="generate_test_script")
                    for i, line in enumerate(script_lines[-5:]):
                        debug(f"  {len(script_lines)-4+i}: {line}",
                              stage="ai_generation", operation="generate_test_script",
                              context={'line_number': len(script_lines)-4+i, 'line_content': line})
            else:
                debug("Step 2 script is empty or None before merging",
                      stage="ai_generation", operation="generate_test_script",
                      context={'step_2_script_empty': True})

        # Get the previous step's script if available
        previous_script = None
        if state and step_table_entry:
            step_no = step_table_entry.get('step_no')
            if step_no and hasattr(state, 'previous_scripts'):
                # Find the previous step number
                try:
                    current_step_no = int(step_no)
                    if current_step_no > 1:
                        previous_step_no = str(current_step_no - 1)
                        previous_script = state.previous_scripts.get(previous_step_no)
                        if previous_script:
                            debug(f"Found previous script for step {previous_step_no} ({len(previous_script)} chars)",
                                  stage="ai_generation", operation="generate_test_script",
                                  context={'previous_step_no': previous_step_no, 'previous_script_length': len(previous_script)})

                            # DEBUG: For Step 2, log details about the previous script
                            if step_no == '2':
                                debug("=== DEBUG: Previous Script (Step 1) Details ===",
                                      stage="ai_generation", operation="generate_test_script")
                                prev_script_lines = previous_script.split('\n')
                                debug(f"Previous script length: {len(prev_script_lines)} lines",
                                      stage="ai_generation", operation="generate_test_script",
                                      context={'previous_script_lines': len(prev_script_lines)})
                                debug("First 10 lines of previous script:",
                                      stage="ai_generation", operation="generate_test_script")
                                for i, line in enumerate(prev_script_lines[:10]):
                                    debug(f"  {i+1}: {line}",
                                          stage="ai_generation", operation="generate_test_script",
                                          context={'line_number': i+1, 'line_content': line})
                        else:
                            debug(f"No previous script found for step {previous_step_no}",
                                  stage="ai_generation", operation="generate_test_script",
                                  context={'previous_step_no': previous_step_no, 'script_found': False})
                except (ValueError, TypeError):
                    debug(f"Invalid step number: {step_no}",
                          stage="ai_generation", operation="generate_test_script",
                          context={'invalid_step_no': step_no})

        # Merge the current script with the previous script if available
        if previous_script:
            debug("Merging current script with previous script via AI with enhanced prompt generation tracing",
                  stage="ai_generation", operation="generate_test_script")
            merged_script = merge_scripts_with_ai(
                previous_script=previous_script,
                current_script=step_specific_script,
                api_key=api_key,
                context={
                    'test_case_id': test_case.get('Test Case ID', 'unknown'),
                    'step_no': step_table_entry.get('step_no') if step_table_entry else 'unknown',
                    'action': step_table_entry.get('action') if step_table_entry else 'unknown',
                    'previous_script_length': len(previous_script),
                    'current_script_length': len(step_specific_script),
                    'is_prompt_generation': True  # Enable prompt generation tracing
                }
            )
            debug(f"Merged script generated ({len(merged_script)} chars)",
                  stage="ai_generation", operation="generate_test_script",
                  context={'merged_script_length': len(merged_script)})

            # DEBUG: For Step 2, log the merged script
            if step_table_entry and step_table_entry.get('step_no') == '2':
                debug("=== DEBUG: Step 2 Merged Script ===",
                      stage="ai_generation", operation="generate_test_script")
                if merged_script:
                    # Log a summary of the merged script
                    merged_lines = merged_script.split('\n')
                    debug(f"Merged script length: {len(merged_lines)} lines",
                          stage="ai_generation", operation="generate_test_script",
                          context={'merged_script_lines': len(merged_lines)})
                    debug("First 10 lines of merged script:",
                          stage="ai_generation", operation="generate_test_script")
                    for i, line in enumerate(merged_lines[:10]):
                        debug(f"  {i+1}: {line}",
                              stage="ai_generation", operation="generate_test_script",
                              context={'line_number': i+1, 'line_content': line})
                    if len(merged_lines) > 15:
                        debug("Last 5 lines of merged script:",
                              stage="ai_generation", operation="generate_test_script")
                        for i, line in enumerate(merged_lines[-5:]):
                            debug(f"  {len(merged_lines)-4+i}: {line}",
                                  stage="ai_generation", operation="generate_test_script",
                                  context={'line_number': len(merged_lines)-4+i, 'line_content': line})
                else:
                    debug("Merged script is empty or None after merging",
                          stage="ai_generation", operation="generate_test_script",
                          context={'merged_script_empty': True})
        else:
            debug("No previous script to merge with - using step-specific script as final output",
                  stage="ai_generation", operation="generate_test_script")

        # Update script continuity tracking if state is provided
        if state and step_table_entry:
            step_no = step_table_entry.get("step_no")
            if step_no:
                # DEBUG: Log state before update
                if step_no == '2':
                    debug("=== DEBUG: State Before Update ===",
                          stage="ai_generation", operation="generate_test_script")
                    if hasattr(state, 'previous_scripts'):
                        debug(f"State previous_scripts keys: {list(state.previous_scripts.keys()) if state.previous_scripts else 'None'}",
                              stage="ai_generation", operation="generate_test_script",
                              context={'previous_scripts_keys': list(state.previous_scripts.keys()) if state.previous_scripts else None})
                    if hasattr(state, 'script_imports'):
                        debug(f"State script_imports count: {len(state.script_imports) if state.script_imports else 0}",
                              stage="ai_generation", operation="generate_test_script",
                              context={'script_imports_count': len(state.script_imports) if state.script_imports else 0})
                    if hasattr(state, 'script_variables'):
                        debug(f"State script_variables keys: {list(state.script_variables.keys()) if state.script_variables else 'None'}",
                              stage="ai_generation", operation="generate_test_script",
                              context={'script_variables_keys': list(state.script_variables.keys()) if state.script_variables else None})

                # Store the merged script (not the step-specific script) for continuity
                # Primary approach: use the update_script_continuity method if available
                if hasattr(state, "update_script_continuity"):
                    state.update_script_continuity(merged_script, step_no)
                    debug(f"Updated script continuity tracking for step {step_no}",
                          stage="ai_generation", operation="generate_test_script",
                          context={'step_no': step_no, 'continuity_updated': True})

                    # DEBUG: Log state after update
                    if step_no == '2':
                        debug("=== DEBUG: State After Update ===",
                              stage="ai_generation", operation="generate_test_script")
                        if hasattr(state, 'previous_scripts'):
                            debug(f"State previous_scripts keys: {list(state.previous_scripts.keys()) if state.previous_scripts else 'None'}",
                                  stage="ai_generation", operation="generate_test_script",
                                  context={'previous_scripts_keys_after': list(state.previous_scripts.keys()) if state.previous_scripts else None})
                        if hasattr(state, 'script_imports'):
                            debug(f"State script_imports count: {len(state.script_imports) if state.script_imports else 0}",
                                  stage="ai_generation", operation="generate_test_script",
                                  context={'script_imports_count_after': len(state.script_imports) if state.script_imports else 0})
                        if hasattr(state, 'script_variables'):
                            debug(f"State script_variables keys: {list(state.script_variables.keys()) if state.script_variables else 'None'}",
                                  stage="ai_generation", operation="generate_test_script",
                                  context={'script_variables_keys_after': list(state.script_variables.keys()) if state.script_variables else None})
                else:
                    # Fallback: at least remember the raw script
                    if not hasattr(state, "previous_scripts"):
                        state.previous_scripts = {}
                    state.previous_scripts[str(step_no)] = merged_script
                    debug(f"(fallback) Stored merged script for step {step_no} in state.previous_scripts",
                          stage="ai_generation", operation="generate_test_script",
                          context={'step_no': step_no, 'fallback_storage': True})

        # =====================================================================
        # Final Script Preparation
        # =====================================================================
        # Add clear headers to both scripts to indicate their purpose and scope

        # Add a comment to the step-specific script to indicate it's only for the current step
        step_no = step_table_entry.get('step_no') if step_table_entry else 'unknown'
        step_specific_script = f"""# =========================================================================
# STEP-SPECIFIC SCRIPT - IMPLEMENTS ONLY STEP {step_no}
# =========================================================================
# This script focuses exclusively on implementing the functionality for
# test case step {step_no} without any attempt to merge with previous steps.
# It is useful for debugging and understanding the specific implementation
# of this individual step.
# =========================================================================

{step_specific_script}"""

        # Add a comment to the merged script to indicate it's the final merged script
        merged_script = f"""# =========================================================================
# MERGED SCRIPT - IMPLEMENTS ALL STEPS UP TO STEP {step_no}
# =========================================================================
# This script combines the implementation of all test case steps up to and
# including step {step_no}. It maintains proper continuity between steps,
# preserves browser state, and ensures a cohesive test flow.
# =========================================================================

{merged_script}"""

        # Return both the merged script and the step-specific script as a tuple
        # The merged script is returned first as it's the primary output used for execution
        return merged_script, step_specific_script
    except Exception as e:
        debug(f"Error generating test script: {e}",
              stage="ai_generation", operation="generate_test_script",
              context={'error': str(e), 'error_type': type(e).__name__})
        error_message = f"Error: {str(e)}"
        return error_message, error_message