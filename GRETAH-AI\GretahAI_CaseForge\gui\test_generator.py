#!/usr/bin/env python3
"""
GretahAI Test Generator Module
=============================

This module provides a comprehensive Streamlit-based test case generation interface for the GretahAI CaseForge system.
This module has been modularized to improve maintainability and organization.

MODULARIZATION:
The original large file has been split into focused modules:
- helpers/: Modular helper functions organized by domain (jira, ui, data, file, utils, etc.)
- db_helper/: Centralized database operations package (replaces test_generator_db_helpers.py)
- test_generator.py: Main UI orchestration and rendering

PRIMARY PURPOSE:
- JIRA integration for extracting issue details and attachments
- AI-powered test case generation using local (Ollama) or cloud (Google AI Studio) models
- Database management for storing and retrieving test cases
- Excel file generation and management for test case export
- User interface components for test case visualization and editing

MAIN FUNCTIONS:
- render_jira_details(): UI rendering for JIRA issue display with enhancement toggle
- render_test_generator(): Main UI orchestration function (still large, further modularization planned)
"""

import base64
import io
from pathlib import Path
from datetime import datetime
import os
import re
import traceback
import pandas as pd
import streamlit as st
import json

# Import database helper (uses modularized db_helper package)
import db_helper
import sqlite3

# Import utility functions from gui.utils (modularized version)
from gui.utils import (
    generate_test_scenarios,
    merge_excel_files, 
    load_usage_data,
    save_usage_data
)

# Import all functions from helpers package (consolidated imports)
from helpers import (
    # Excel and file operations
    create_formatted_excel_from_scenarios,
    get_latest_test_case_file,
    upload_edited_excel,
    process_attachment_for_display,
    
    # CSV operations
    export_test_cases_to_csv,
    format_csv_for_external_tools,
    
    # AI integration
    is_ollama_running,
    run_ollama_with_chat,
    run_google_ai_studio,
    generate_gemini_test_gen_prompt,
    parse_test_scenarios_json,  # <-- robust version
    
    # JIRA operations
    handle_jira_extraction_workflow,
    handle_jira_enhancement_workflow,
    extract_jira_issue,
    parse_enhanced_description_json,
    format_jira_description,
    
    # UI components
    create_jira_details_css,
    render_jira_extraction_form,
    render_enhancement_form,
    initialize_session_state,
    render_test_generation_controls,
    render_generate_button,
    show_extraction_guidance,
    format_test_type_display_name,
    create_attachment_grid_html,
    render_jira_issue_section,
    
    # Data validation
    validate_test_case_dataframe,
    count_valid_test_cases
)

from db_helper import (
    get_or_create_test_run,
    save_test_cases_to_database,
    get_latest_test_cases,
    get_most_recent_test_cases_exact,
    process_backup_style_test_data,
    update_test_run_completion,
    get_enhanced_jira_description,
    save_enhanced_jira_description,
    count_test_cases_in_database_output,
    validate_database_connection,
    cleanup_orphaned_test_data,
    add_single_test_case,
    add_test_step_to_case,
    delete_test_step,
    delete_test_case
)

# Import tab rendering functions
from gui.test_generator_tabs import (
    render_test_results_tabs,
    render_raw_ai_output_expander
)

def initialize_module_session_state():
    """Initialize session state variables for this module."""
    # Store generated data
    if "scenario_data" not in st.session_state:
        st.session_state.scenario_data = None

    # Load Google AI usage data from file
    initial_request_timestamps, initial_token_usage = load_usage_data()

    # Store Google AI usage data
    if "google_request_timestamps" not in st.session_state:
        st.session_state.google_request_timestamps = initial_request_timestamps # Store datetime objects
    if "google_token_usage" not in st.session_state:
        st.session_state.google_token_usage = initial_token_usage # Store tuples of (datetime, token_count)

    # Initialize generation issues list if it doesn't exist
    if "generation_issues" not in st.session_state:
        st.session_state.generation_issues = []

def render_jira_details(issue):
    """
    Render comprehensive JIRA issue details using the centralized component.
    """
    # Get enhanced description from database and session state
    enhanced_desc_db, enhanced_timestamp_db = get_enhanced_jira_description(db_helper.DATABASE_PATH, issue.key)
    
    # Check session state for enhanced description (from recent enhancement)
    enhanced_desc_session = st.session_state.get("enhanced_description", None)
    enhanced_timestamp_session = st.session_state.get("enhanced_timestamp", None)
    
    # Use session state if available (more recent), otherwise use database
    if enhanced_desc_session and enhanced_timestamp_session:
        enhanced_desc = enhanced_desc_session
        enhanced_timestamp = enhanced_timestamp_session
    else:
        enhanced_desc = enhanced_desc_db
        enhanced_timestamp = enhanced_timestamp_db
        # Store in session state for consistency
        st.session_state["enhanced_description"] = enhanced_desc
        st.session_state["enhanced_timestamp"] = enhanced_timestamp

    # Use the centralized render function that properly handles markdown formatting
    render_jira_issue_section(issue, enhanced_desc, enhanced_timestamp)

    # Handle attachments (keep existing attachment logic)
    if issue and hasattr(issue, 'fields') and hasattr(issue.fields, 'attachment') and issue.fields.attachment:
        st.markdown('<div class="jira-details"><h3>Attachments</h3></div>', unsafe_allow_html=True)

        # Create a directory for storing attachments if it doesn't exist
        attached_images_dir = Path("attached_images")
        attached_images_dir.mkdir(exist_ok=True)

        # Count image attachments first
        image_attachments = [
            att for att in issue.fields.attachment
            if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
        ]

        if image_attachments:
            # Create a simple container for attachments
            with st.container():
                # Add CSS specifically to fix Streamlit's fullscreen modal centering
                st.markdown("""
                <style>
                /* Fix Streamlit's native fullscreen modal centering */
                div[data-modal-container="true"] .stImage > div {
                    display: flex !important;
                    justify-content: center !important;
                    align-items: center !important;
                    height: 100% !important;
                }
                
                div[data-modal-container="true"] .stImage img {
                    object-fit: contain !important;
                    max-width: 100% !important;
                    max-height: 100% !important;
                }
                
                /* Ensure fullscreen overlay centers properly */
                .stImage [data-testid="fullScreen"] {
                    display: flex !important;
                    justify-content: center !important;
                    align-items: center !important;
                }
                </style>
                """, unsafe_allow_html=True)

                # Display images in a grid layout
                cols = st.columns(min(3, len(image_attachments)))  # Max 3 columns
                
                for idx, att in enumerate(image_attachments):
                    col_idx = idx % 3
                    
                    with cols[col_idx]:
                        try:
                            # Download and save the image
                            img_data = att.get()
                            img_path = attached_images_dir / att.filename
                            
                            with open(img_path, 'wb') as f:
                                f.write(img_data)

                            # Display image with built-in fullscreen capability - pure Streamlit
                            st.image(
                                img_data, 
                                caption=att.filename,
                                width=300  # Set fixed width to fit properly in column
                            )

                        except Exception as e:
                            st.error(f"Error processing image {att.filename}: {str(e)}")

        # Display non-image attachments
        non_image_attachments = [
            att for att in issue.fields.attachment
            if not any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
        ]

        if non_image_attachments:
            st.markdown("### Other Attachments")
            for att in non_image_attachments:
                col1, col2 = st.columns([4, 1])
                with col1:
                    st.markdown(f"📎 {att.filename}")
                with col2:
                    try:
                        # Download the attachment
                        attachment_data = att.get()
                        st.download_button(
                            label="💾 Download",
                            data=attachment_data,
                            file_name=att.filename,
                            mime="application/octet-stream",
                            key=f"download_other_{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                        )
                    except Exception as e:
                        st.error(f"Error processing attachment {att.filename}: {str(e)}")


def render_test_case_management_section():
    """
    Render the Test Case Management section with buttons for adding/deleting test cases and steps.

    This section appears after test cases are successfully generated and displayed,
    providing users with the ability to manually manage their test cases.
    """
    # Only show if we have the necessary context
    current_jira_id = st.session_state.get("current_jira_id")
    current_test_type = st.session_state.get("current_test_type", "positive")
    current_user = st.session_state.get("admin_username", "anonymous")

    if not current_jira_id:
        return

    # Import the clear cache function for refreshing data
    from gui.unified_interface_components import clear_all_cache_for_fresh_data

    with st.expander("🔧 Test Case Management", expanded=False):
        st.markdown("**Manage your test cases and test steps:**")

        # Create four columns for the buttons
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("➕ Add Test Case", help="Add a new test case", use_container_width=True):
                st.session_state.show_add_test_case_form = True

        with col2:
            if st.button("📝 Add Test Step", help="Add a step to an existing test case", use_container_width=True):
                st.session_state.show_add_test_step_form = True

        with col3:
            if st.button("🗑️ Delete Test Case", help="Remove an entire test case", use_container_width=True):
                st.session_state.show_delete_test_case_form = True

        with col4:
            if st.button("❌ Delete Test Step", help="Remove a specific test step", use_container_width=True):
                st.session_state.show_delete_test_step_form = True

        # Add Test Case Form
        if st.session_state.get("show_add_test_case_form", False):
            st.markdown("---")
            st.markdown("**Add New Test Case**")

            with st.form("add_test_case_form"):
                objective = st.text_area("Test Case Objective", placeholder="Describe what this test case will verify...")
                priority = st.selectbox("Priority", ["High", "Medium", "Low"], index=1)
                prerequisite = st.text_area("Prerequisites", placeholder="Any setup or conditions required...")

                col_submit, col_cancel = st.columns([1, 1])
                with col_submit:
                    submitted = st.form_submit_button("Add Test Case", use_container_width=True)
                with col_cancel:
                    cancelled = st.form_submit_button("Cancel", use_container_width=True)

                if submitted and objective.strip():
                    success, test_case_id, message = add_single_test_case(
                        db_helper.DATABASE_PATH, current_jira_id, objective.strip(),
                        current_test_type, current_user, priority, prerequisite.strip()
                    )

                    if success:
                        st.success(f"✅ {message}")
                        clear_all_cache_for_fresh_data()
                        st.session_state.show_add_test_case_form = False
                        st.rerun()
                    else:
                        st.error(f"❌ {message}")

                elif submitted:
                    st.error("Please enter a test case objective.")

                if cancelled:
                    st.session_state.show_add_test_case_form = False
                    st.rerun()

        # Add Test Step Form
        if st.session_state.get("show_add_test_step_form", False):
            st.markdown("---")
            st.markdown("**Add Test Step to Existing Test Case**")

            # Get available test cases for the current JIRA ID
            try:
                from db_helper import get_test_cases_from_database
                test_cases_df = get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, current_test_type)

                if not test_cases_df.empty:
                    available_test_cases = test_cases_df['Test Case ID'].dropna().unique().tolist()

                    with st.form("add_test_step_form"):
                        selected_test_case = st.selectbox("Select Test Case", available_test_cases)
                        test_step = st.text_area("Test Step", placeholder="Describe the action to perform...")
                        expected_result = st.text_area("Expected Result", placeholder="What should happen when this step is executed...")

                        col_submit, col_cancel = st.columns([1, 1])
                        with col_submit:
                            submitted = st.form_submit_button("Add Test Step", use_container_width=True)
                        with col_cancel:
                            cancelled = st.form_submit_button("Cancel", use_container_width=True)

                        if submitted and test_step.strip() and expected_result.strip():
                            success, step_number, message = add_test_step_to_case(
                                db_helper.DATABASE_PATH, selected_test_case,
                                test_step.strip(), expected_result.strip(), current_user
                            )

                            if success:
                                st.success(f"✅ {message}")
                                clear_all_cache_for_fresh_data()
                                st.session_state.show_add_test_step_form = False
                                st.rerun()
                            else:
                                st.error(f"❌ {message}")

                        elif submitted:
                            st.error("Please fill in both test step and expected result.")

                        if cancelled:
                            st.session_state.show_add_test_step_form = False
                            st.rerun()
                else:
                    st.info("No test cases found. Please add a test case first.")
                    if st.button("Close"):
                        st.session_state.show_add_test_step_form = False
                        st.rerun()

            except Exception as e:
                st.error(f"Error loading test cases: {str(e)}")
                if st.button("Close"):
                    st.session_state.show_add_test_step_form = False
                    st.rerun()

        # Delete Test Case Form
        if st.session_state.get("show_delete_test_case_form", False):
            st.markdown("---")
            st.markdown("**Delete Test Case**")

            try:
                from db_helper import get_test_cases_from_database
                test_cases_df = get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, current_test_type)

                if not test_cases_df.empty:
                    available_test_cases = test_cases_df['Test Case ID'].dropna().unique().tolist()

                    with st.form("delete_test_case_form"):
                        selected_test_case = st.selectbox("Select Test Case to Delete", available_test_cases)
                        st.warning("⚠️ This will permanently delete the test case and all its steps!")

                        col_submit, col_cancel = st.columns([1, 1])
                        with col_submit:
                            submitted = st.form_submit_button("Delete Test Case", type="primary", use_container_width=True)
                        with col_cancel:
                            cancelled = st.form_submit_button("Cancel", use_container_width=True)

                        if submitted:
                            success = delete_test_case(db_helper.DATABASE_PATH, selected_test_case)

                            if success:
                                st.success(f"✅ Successfully deleted test case {selected_test_case}")
                                clear_all_cache_for_fresh_data()
                                st.session_state.show_delete_test_case_form = False
                                st.rerun()
                            else:
                                st.error(f"❌ Failed to delete test case {selected_test_case}")

                        if cancelled:
                            st.session_state.show_delete_test_case_form = False
                            st.rerun()
                else:
                    st.info("No test cases found to delete.")
                    if st.button("Close"):
                        st.session_state.show_delete_test_case_form = False
                        st.rerun()

            except Exception as e:
                st.error(f"Error loading test cases: {str(e)}")
                if st.button("Close"):
                    st.session_state.show_delete_test_case_form = False
                    st.rerun()

        # Delete Test Step Form
        if st.session_state.get("show_delete_test_step_form", False):
            st.markdown("---")
            st.markdown("**Delete Test Step**")

            try:
                from db_helper import get_test_cases_from_database
                test_cases_df = get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, current_test_type)

                if not test_cases_df.empty:
                    available_test_cases = test_cases_df['Test Case ID'].dropna().unique().tolist()

                    with st.form("delete_test_step_form"):
                        selected_test_case = st.selectbox("Select Test Case", available_test_cases)

                        # Get step numbers for the selected test case
                        case_steps = test_cases_df[test_cases_df['Test Case ID'] == selected_test_case]['Step No'].dropna().unique()
                        step_numbers = sorted([int(step) for step in case_steps if str(step).isdigit()])

                        if step_numbers:
                            selected_step = st.selectbox("Select Step Number to Delete", step_numbers)
                            st.warning("⚠️ This will permanently delete the selected test step!")

                            col_submit, col_cancel = st.columns([1, 1])
                            with col_submit:
                                submitted = st.form_submit_button("Delete Test Step", type="primary", use_container_width=True)
                            with col_cancel:
                                cancelled = st.form_submit_button("Cancel", use_container_width=True)

                            if submitted:
                                success, message = delete_test_step(db_helper.DATABASE_PATH, selected_test_case, selected_step)

                                if success:
                                    st.success(f"✅ {message}")
                                    clear_all_cache_for_fresh_data()
                                    st.session_state.show_delete_test_step_form = False
                                    st.rerun()
                                else:
                                    st.error(f"❌ {message}")

                            if cancelled:
                                st.session_state.show_delete_test_step_form = False
                                st.rerun()
                        else:
                            st.info("No test steps found for the selected test case.")
                            if st.button("Close"):
                                st.session_state.show_delete_test_step_form = False
                                st.rerun()
                else:
                    st.info("No test cases found.")
                    if st.button("Close"):
                        st.session_state.show_delete_test_step_form = False
                        st.rerun()

            except Exception as e:
                st.error(f"Error loading test cases: {str(e)}")
                if st.button("Close"):
                    st.session_state.show_delete_test_step_form = False
                    st.rerun()


def render_test_generator(jira_client, ai_provider, google_api_key, selected_model, config=None):
    """
    Main test generator interface orchestration function.
    
    FUNCTION TYPE: MAIN UI RENDERING FUNCTION (VERY LARGE - NEEDS MODULARIZATION)
    
    This is the primary function that orchestrates the entire test generation workflow.
    It handles multiple concerns and is over 2000+ lines long, making it a prime candidate 
    for modularization into smaller, focused functions.

    PRIMARY RESPONSIBILITIES:
    1. UI Layout Management:
       - Header rendering and page structure
       - Input forms for JIRA ID and generation parameters
       - Tab organization for different views
       - Modal dialogs for images and PDFs

    2. JIRA Integration:
       - JIRA issue extraction workflow
       - Enhanced description generation and storage
       - Attachment handling and display

    3. AI-Powered Test Generation:
       - Integration with multiple AI providers (Local Ollama, Google AI Studio)
       - Temperature control for generation strictness
       - Support for different test types (positive, negative, security, performance)
       - Batch generation for "all" test types

    4. Database Operations:
       - Test case storage and retrieval
       - Test run management and tracking
       - User session management
       - Enhanced description caching

    5. File Management:
       - Excel file generation and formatting
       - Temporary file creation for downloads
       - Attachment downloads and storage

    6. State Management:
       - Session state initialization and updates
       - Error tracking and issue reporting
       - User authentication validation

    MODULARIZATION OPPORTUNITIES:
    - extract_ui_layout_management()
    - handle_jira_extraction_workflow()
    - handle_test_case_generation()
    - render_test_results_tabs()
    - manage_file_operations()
    - handle_database_operations()

    Args:
        jira_client: JIRA client instance or None
            - Authenticated JIRA client for API operations
            - None if JIRA connection failed/unavailable
        ai_provider (str): AI service provider
            - "Local" for Ollama local models
            - "Google AI Studio" for Google's cloud models
        google_api_key (str): API key for Google AI Studio
            - Required when ai_provider is "Google AI Studio"
            - Can be empty string for local providers
        selected_model (str): AI model name
            - For Local: Ollama model names (e.g., "llama2", "mistral")
            - For Google: Model names (e.g., "gemini-pro")
        config (dict, optional): Configuration settings
            - Additional configuration parameters
            - Currently optional and not heavily used

    Returns:
        None (renders directly to Streamlit interface)

    Session State Variables Used:
        - google_request_timestamps: API usage tracking
        - jira_issue_extracted: Whether JIRA issue is loaded
        - jira_issue: Current JIRA issue object
        - scenario_data: Generated test case data
        - enhanced_description: Cached enhanced descriptions
        - generation_issues: Error tracking list
        - modal_image: Image modal display state
        - use_enhanced_description: Toggle for enhanced descriptions

    Database Operations:
        - Creates and manages test runs
        - Stores and retrieves test cases
        - Manages enhanced descriptions
        - Tracks user sessions and activities

    File Operations:
        - Creates temporary Excel files
        - Manages attachment downloads
        - Handles image storage and display

    UI Components:
        - JIRA extraction form
        - Test generation controls
        - Result display tabs (Raw AI Output, Generated Test Cases, Most Recent)
        - Download buttons and file management
        - Error display and issue tracking

    Error Handling:
        - Comprehensive exception catching and logging
        - User-friendly error messages
        - Issue tracking in session state
        - Fallback mechanisms for failed operations    """
    global db_helper  # ensure db_helper refers to module-level import, not local    
    
    # Initialize session state variables
    initialize_module_session_state()
    initialize_session_state()

    # Test Case Generator Page
    # Main content area header
    st.markdown('<h1 class="main-header">GretahAI Test Case Generator</h1>', unsafe_allow_html=True)    # Initialize session state for JIRA issue if not exists
    if "jira_issue_extracted" not in st.session_state:
        st.session_state.jira_issue_extracted = False
        st.session_state.jira_issue = None

    # Render JIRA extraction form
    case_id, extract_button, temperature = render_jira_extraction_form(jira_client)

    # Render enhancement form
    enhance_button, additional_context = render_enhancement_form(jira_client)    # Handle JIRA extraction
    jira_connected = jira_client is not None
    if extract_button and jira_connected:
        with st.spinner("Extracting JIRA issue..."):
            success, result = extract_jira_issue(jira_client, case_id)
              
            if success:
                # Validate issue type before proceeding
                if hasattr(result.fields, 'issuetype') and result.fields.issuetype:
                    issue_type = result.fields.issuetype.name
                    if issue_type.lower() == 'story':
                        # Set session state variables immediately for Story issues
                        st.session_state.jira_issue_extracted = True
                        st.session_state.jira_issue = result
                        
                        # Clear previous test case generation data when extracting a new JIRA issue
                        st.session_state.scenario_data = {
                            "issue": None,
                            "response": None,
                            "output_file": None,
                            "processing_time": None,
                            "ai_provider": st.session_state.get("ai_provider_radio", "Local"),
                            "model_used": None,
                            "tokens_used": None
                        }
                          
                        # Load existing enhanced description from DB
                        try:
                            ed, ts = db_helper.get_jira_issue_enhancement(db_helper.DATABASE_PATH, case_id)
                            if ed:
                                st.session_state.enhanced_description = ed
                                st.session_state.enhanced_timestamp = ts
                            else:
                                st.session_state.enhanced_description = None
                                st.session_state.enhanced_timestamp = None
                        except Exception as e:
                            print(f"Error loading enhanced description: {e}")
                            st.session_state.enhanced_description = None
                            st.session_state.enhanced_timestamp = None
                        
                        st.success(f"Successfully extracted JIRA Story: {result.key}")
                        st.rerun()
                    else:
                        # Show concise error just below the note
                        st.error("Only JIRA issues of type **Story** are supported.")
                        st.session_state.jira_issue_extracted = False
                        st.session_state.jira_issue = None
                else:
                    st.error("Unable to determine issue type. Please check the JIRA issue.")
                    st.session_state.jira_issue_extracted = False
                    st.session_state.jira_issue = None
            else:
                st.error("Only JIRA issues of type **Story** are supported.")
                st.session_state.jira_issue_extracted = False
                st.session_state.jira_issue = None
                
                # Also clear scenario data on failed extraction
                st.session_state.scenario_data = {
                    "issue": None,
                    "response": None,
                    "output_file": None,
                    "processing_time": None,
                    "ai_provider": st.session_state.get("ai_provider_persistent", "Cloud"),
                    "model_used": None,
                    "tokens_used": None
                }
        # After extraction, the page will rerun due to st.rerun() in the workflow

    # Handle enhancement - check if button was pressed and JIRA issue is available
    if enhance_button and st.session_state.get("jira_issue_extracted", False) and st.session_state.get("jira_issue", None) is not None:
        handle_jira_enhancement_workflow(jira_client, case_id, ai_provider, selected_model, google_api_key, temperature)    # Display JIRA details in an expandable section if extraction was successful
    if st.session_state.jira_issue_extracted and st.session_state.jira_issue:
        with st.expander("JIRA Issue Details", expanded=True):
            render_jira_details(st.session_state.jira_issue)

        # Render test generation controls
        test_type, num_scenarios = render_test_generation_controls()
        
        # Render generate button
        generate_button = render_generate_button(ai_provider, google_api_key)
    else:
        # If JIRA issue not extracted yet, show a message to guide the user
        show_extraction_guidance(extract_button)

    # We'll only show the issues expander inside the tabs, not at the top level    # Generate test cases when the button is clicked
    if st.session_state["current_page"] == "generator" and "test_generator_generate_button" in st.session_state and st.session_state.test_generator_generate_button and st.session_state.jira_issue_extracted:
        # Clear any previous issues
        st.session_state.generation_issues = []

        # Track test case ID before generation starts (for range detection)
        from gui.unified_interface_components import track_test_case_id_before_generation
        track_test_case_id_before_generation()

        with st.spinner(f"Generating test cases-- Please wait."):
            try:                # Get the appropriate description based on toggle state
                use_enhanced = st.session_state.get("use_enhanced_description", False)
                
                # If enhanced toggle is enabled, fetch enhanced description from database
                if use_enhanced:
                    # Fetch enhanced description from jira_issues table
                    enhanced_desc_from_db, enhanced_timestamp_from_db = get_enhanced_jira_description(
                        db_helper.DATABASE_PATH, case_id
                    )
                    
                    if enhanced_desc_from_db:
                        st.session_state["using_enhanced_for_generation"] = True
                        description_for_generation = enhanced_desc_from_db
                        st.session_state["description_source"] = "enhanced"
                        print(f"Using enhanced description from database for {case_id} (enhanced on: {enhanced_timestamp_from_db})")
                    else:
                        st.session_state["using_enhanced_for_generation"] = False
                        description_for_generation = st.session_state.jira_issue.fields.description
                        st.session_state["description_source"] = "original"
                        print(f"Enhanced description requested but not found in database for {case_id}, using original description")
                else:
                    st.session_state["using_enhanced_for_generation"] = False
                    description_for_generation = st.session_state.jira_issue.fields.description
                    st.session_state["description_source"] = "original"
                
                if test_type == "all":
                    all_test_types = ["positive", "negative", "security", "performance"]
                    all_responses = []
                    all_output_files = []
                    all_issues = []
                    total_processing_time = 0
                    total_tokens_used = 0

                    progress_bar = st.progress(0)
                    status_text = st.empty()                    # We'll store raw responses in the Test_cases folder
                    test_cases_dir = Path("Test_cases")
                    test_cases_dir.mkdir(exist_ok=True)

                    # We'll use the database to track test case IDs, no need to check Excel files
                    # These variables are kept for consistency with gui.py
                    _ = 0  # highest_id (unused)
                    _ = False  # main_file_exists (unused)
                    _ = None  # main_all_file (unused)

                    # Create a test run for all test types                    # Get the current user from session state (if logged in)
                    current_user = st.session_state.get("admin_username", "anonymous")
                    test_run_id = get_or_create_test_run(
                        db_helper.DATABASE_PATH,
                        case_id,
                        "all",  # Use "all" as the test type for the test run
                        current_user,
                        f"Generated with {ai_provider} using {selected_model} - All test types"
                    )

                    # Process each test type
                    for i, tt in enumerate(all_test_types):
                        # Update progress
                        progress = (i) / len(all_test_types)
                        progress_bar.progress(progress)                        # Get test type display name using helper function
                        test_type_name = format_test_type_display_name(tt)

                        status_text.info(f"Generating {test_type_name} ({i+1}/{len(all_test_types)})...")

                        try:
                            # Pass the appropriate description to generate_test_scenarios
                            issue, response, output_filename, processing_time, tokens_used = generate_test_scenarios(
                                case_id, tt, num_scenarios, selected_model, jira_client, ai_provider, google_api_key,
                                is_all_test_types=True, continue_numbering=True, test_run_id=test_run_id,
                                custom_description=description_for_generation  # Pass the selected description
                            )
                            

                            # If generation failed but didn't raise an exception, add a warning
                            if not response or not output_filename:
                                error_msg = f"Failed to generate {tt.upper()} test cases. Please check the AI response for details."
                                if "generation_issues" not in st.session_state:
                                    st.session_state.generation_issues = []
                                st.session_state.generation_issues.append(error_msg)
                                print(f"Added issue: {error_msg}")
                            else:
                                # Only append results if generation was successful
                                all_responses.append(response)
                                all_output_files.append(output_filename)
                                all_issues.append(issue)
                                total_processing_time += processing_time
                                if tokens_used:
                                    total_tokens_used += tokens_used

                        except Exception as e:
                            # If an exception occurred, add it to the generation_issues list
                            error_msg = f"Error generating {tt.upper()} test cases: {str(e)}"
                            if "generation_issues" not in st.session_state:
                                st.session_state.generation_issues = []
                            st.session_state.generation_issues.append(error_msg)
                            print(f"Added issue: {error_msg}")
                            st.error(f"Error generating {tt.upper()} test cases: {str(e)}")

                            # Add empty/null values when generation fails
                            all_responses.append(None)
                            all_output_files.append(None)
                            if 'issue' in locals():
                                all_issues.append(issue)  # Use the extracted issue if available
                            else:
                                all_issues.append(st.session_state.jira_issue)  # Fall back to session state issue
                            total_processing_time += 0
                            total_tokens_used += 0

                    # Complete the progress bar
                    progress_bar.progress(1.0)
                    # Success message removed as requested
                    status_text.empty()

                    # Update the test run with the total number of test cases and any errors
                    if test_run_id:
                        # Count successful and failed test types
                        successful_types = []
                        failed_types = []
                        total_test_cases = 0

                        for _, (tt, resp, out_file) in enumerate(zip(all_test_types, all_responses, all_output_files)):
                            if resp and out_file:
                                successful_types.append(tt)

                                # Count the actual number of test cases generated for this test type
                                try:
                                    if out_file and out_file.startswith("database://"):
                                        # Extract JIRA ID and test type from the database URL
                                        parts = out_file.split("/")
                                        if len(parts) >= 4:
                                            db_jira_id = parts[2]
                                            db_test_type = parts[3]                                            # Get the test cases from the database
                                            df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, db_jira_id, tt)

                                            # Use helper function to count valid test cases
                                            test_cases_count = count_valid_test_cases(df)
                                            if test_cases_count > 0:
                                                # Add to total
                                                total_test_cases += test_cases_count
                                                print(f"Counted {test_cases_count} test cases for {tt.upper()} from database")
                                            else:                                                # Fallback to expected count
                                                total_test_cases += num_scenarios
                                                print(f"Using expected count of {num_scenarios} for {tt.upper()} (no valid test cases found in database)")
                                    elif out_file and os.path.exists(out_file):
                                        # Read the Excel file
                                        df = pd.read_excel(out_file)

                                        # Use helper function to count valid test cases
                                        test_cases_count = count_valid_test_cases(df)
                                        if test_cases_count > 0:
                                            # Add to total
                                            total_test_cases += test_cases_count
                                            print(f"Counted {test_cases_count} test cases for {tt.upper()} from Excel file")
                                        else:
                                            # Fallback to expected count
                                            total_test_cases += num_scenarios
                                            print(f"Using expected count of {num_scenarios} for {tt.upper()} (no Test Case ID column in Excel file)")
                                    else:
                                        # Fallback to expected count
                                        total_test_cases += num_scenarios
                                        print(f"Using expected count of {num_scenarios} for {tt.upper()} (no valid output file)")
                                except Exception as e:
                                    # If counting fails, use the expected count
                                    total_test_cases += num_scenarios
                                    print(f"Error counting test cases for {tt.upper()}: {e}. Using expected count of {num_scenarios}")

                                    # Add the error to the generation issues
                                    error_msg = f"Error counting test cases for {tt.upper()}: {str(e)}"
                                    if "generation_issues" not in st.session_state:
                                        st.session_state.generation_issues = []
                                    st.session_state.generation_issues.append(error_msg)
                            else:
                                failed_types.append(tt)
                                # Add a message to the generation issues
                                error_msg = f"Failed to generate {tt.upper()} test cases"
                                if "generation_issues" not in st.session_state:
                                    st.session_state.generation_issues = []
                                st.session_state.generation_issues.append(error_msg)

                        # Create a detailed note about the generation
                        if failed_types:
                            notes = f"Generated {total_test_cases} test cases with {ai_provider} using {selected_model}. "
                            notes += f"Successful types: {', '.join(successful_types)}. "
                            notes += f"Failed types: {', '.join(failed_types)}."
                        else:
                            notes = f"Successfully generated {total_test_cases} test cases with {ai_provider} using {selected_model}."                        # Update the test run with the final status
                        update_test_run_completion(
                            db_helper.DATABASE_PATH,
                            test_run_id,
                            total_test_cases,
                            "completed" if not failed_types else "partial",
                            notes
                        )

                    # Combine all responses into a single string
                    combined_response = "\n\n".join([f"=== {tt.upper()} TEST CASES ===\n{resp}" for tt, resp in zip(all_test_types, all_responses)])

                    # Use the last output file as the combined output file
                    combined_output_file = all_output_files[-1] if all_output_files else None

                    # Save results to session state
                    st.session_state.scenario_data = {
                        "issue": all_issues[0],  # Use the first issue (they should all be the same)
                        "response": combined_response,
                        "output_file": combined_output_file,
                        "processing_time": total_processing_time,
                        "ai_provider": ai_provider,
                        "model_used": selected_model,
                        "tokens_used": total_tokens_used,
                        "all_files": all_output_files,  # Store all individual files as well
                        "test_types": all_test_types    # Store the test types
                    }

                    # Mark test cases as successfully generated
                    from gui.unified_interface_components import mark_test_cases_generated
                    mark_test_cases_generated()

                    # Success message removed as requested
                else:
                    # Pass the appropriate description to generate_test_scenarios for single test type
                    issue, response, output_file, processing_time, tokens_used = generate_test_scenarios(
                        case_id, test_type, num_scenarios, selected_model, jira_client, ai_provider, google_api_key,
                        custom_description=description_for_generation  # Pass the selected description
                    )
                    
                    # Save results to session state
                    st.session_state.scenario_data = {
                        "issue": issue,
                        "response": response,
                        "output_file": output_file,
                        "processing_time": processing_time,
                        "ai_provider": ai_provider,
                        "model_used": selected_model,
                        "tokens_used": tokens_used  # Store token count if available
                    }

                    # Mark test cases as successfully generated
                    from gui.unified_interface_components import mark_test_cases_generated
                    mark_test_cases_generated()

            except Exception as e:
                st.error(f"âš ï¸ An error occurred during generation: {e}")
                import traceback
                st.code(traceback.format_exc())
                st.session_state.scenario_data = {
                    "issue": None,
                    "response": None,
                    "output_file": None,
                    "processing_time": None,
                    "ai_provider": st.session_state.get("ai_provider_persistent", "Cloud"),  # Default to "Cloud"
                    "model_used": None,
                    "tokens_used": None
                }

    # Force update the session state if it doesn't exist or doesn't have an output file
    if "scenario_data" not in st.session_state or not st.session_state.scenario_data:
        # Get the current JIRA ID and test type from the input fields
        current_jira_id = st.session_state.get("jira_case_id_input", "")
        current_test_type = st.session_state.get("test_type_select", "all")

        # Initialize with default values
        st.session_state.scenario_data = {
            "issue": None,
            "response": None,
            "output_file": f"database://{current_jira_id}/{current_test_type}/latest" if current_jira_id else None,
            "processing_time": None,
            "ai_provider": st.session_state.get("ai_provider_persistent", "Cloud"),
            "model_used": None,
            "tokens_used": None
        }    # Import unified interface components
    from gui.unified_interface_components import (
        render_unified_test_case_interface,
        should_show_unified_interface,
        reset_test_case_generation_state
    )

    # Check if unified interface should be displayed
    if should_show_unified_interface():
        # Get scenario data for Raw AI Output
        data = st.session_state.scenario_data
        issue = data["issue"]

        # Check if this is the first time showing after generation
        first_time_after_generation = not st.session_state.get("unified_interface_initialized", False)

        # Render unified interface with smart defaults if first time after generation
        render_unified_test_case_interface(apply_smart_defaults=first_time_after_generation)

        # Add Test Case Management feature
        render_test_case_management_section()

        # Add Raw AI Output expander at the bottom of the dashboard
        st.markdown("---")  # Add a separator line
        render_raw_ai_output_expander(data, issue)

    # Optional: Add a button to reset and start over (for testing purposes)
    elif st.session_state.get("current_page") == "generator":
        # Show guidance when interface is not visible
        if not st.session_state.get("test_cases_generated", False):
            st.info("💡 **Generate test cases** using the controls above to view and manage your test cases in the unified interface.")
        elif not st.session_state.get("is_admin_logged_in", False):
            st.info("💡 **Navigate to Test Case Management** from the sidebar to view, edit, and execute your test cases.")

        # Add reset button for development/testing
        if st.button("🔄 Reset Session (Start Over)", help="Reset the session to start fresh"):
            reset_test_case_generation_state()
            # Clear scenario data
            if "scenario_data" in st.session_state:
                del st.session_state["scenario_data"]
            st.rerun()