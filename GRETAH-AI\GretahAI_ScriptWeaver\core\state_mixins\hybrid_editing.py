"""
Hybrid Editing Mixin for GretahAI ScriptWeaver State Management.

This module contains the hybrid editing functionality extracted from StateManager
as part of Phase 2 of the parallel refactoring approach using mixin pattern.

The HybridEditingMixin provides methods for:
- Enabling/disabling hybrid editing mode
- Adding and removing manual steps
- Combining AI-generated and manual steps
- Validating step data consistency
- Synchronizing step tables

This mixin is designed to be used with the StateManager class to provide
hybrid editing capabilities while maintaining clean separation of concerns.

© 2025 Cogniron All Rights Reserved.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime

# Import structured debug function for GRETAH logging compliance
from debug_utils import debug


class HybridEditingMixin:
    """
    Mixin class providing hybrid editing functionality for StateManager.
    
    This mixin contains all methods related to hybrid editing of test steps,
    allowing users to combine AI-generated steps with manually added steps.
    
    The mixin assumes the following state fields exist in the implementing class:
    - hybrid_editing_enabled: bool
    - ai_generated_steps: Optional[List[Dict[str, Any]]]
    - step_insertion_points: Dict[str, List[Dict[str, Any]]]
    - combined_step_table: Optional[List[Dict[str, Any]]]
    - step_table_json: List[Dict[str, Any]]
    - step_table_markdown: str
    - total_steps: int
    - selected_test_case: Optional[Dict[str, Any]]
    """

    def enable_hybrid_editing(self) -> bool:
        """
        Enable hybrid editing mode for the current test case.

        Returns:
            bool: True if hybrid editing was enabled successfully
        """
        # Try to load step data if not available
        if not self.step_table_json:
            try:
                step_table_json = self.get_effective_step_table()
                debug("Loaded step data from JSON storage for enabling hybrid editing",
                      stage="state_management", operation="hybrid_editing_data_loading",
                      context={'steps_count': len(step_table_json)})
            except ValueError as e:
                debug(f"Cannot enable hybrid editing: no step table JSON available and failed to load: {e}",
                      stage="state_management", operation="validation_warning",
                      context={'error': str(e)})
                return False

        if not self.step_table_json:
            debug("Cannot enable hybrid editing: no step table JSON available",
                  stage="state_management", operation="validation_warning")
            return False

        self.hybrid_editing_enabled = True
        self.ai_generated_steps = self.step_table_json.copy()

        # Mark AI steps with origin tracking but allow editing
        for step in self.ai_generated_steps:
            step["_is_ai_generated"] = True
            step["_is_locked"] = False  # Allow editing in hybrid mode
            step["_original_ai_step"] = True  # Track original AI origin
            step["_ai_step_modified"] = False  # Track if step has been modified

        debug("Enabled hybrid editing mode",
              stage="state_management", operation="hybrid_editing",
              context={'step_count': len(self.ai_generated_steps)})
        return True

    def reset_ai_step_to_original(self, step_index: int) -> bool:
        """
        Reset a modified AI step back to its original state.

        Args:
            step_index: Index of the AI step to reset

        Returns:
            bool: True if reset was successful
        """
        if not self.ai_generated_steps or step_index >= len(self.ai_generated_steps):
            return False

        step = self.ai_generated_steps[step_index]

        # Only reset if it was modified and we have original data
        if not step.get('_ai_step_modified', False):
            return False

        # If we have the original step table, restore from there
        if self.step_table_json and step_index < len(self.step_table_json):
            original_step = self.step_table_json[step_index].copy()

            # Preserve the AI flags while restoring original data
            original_step.update({
                "_is_ai_generated": True,
                "_is_locked": False,
                "_original_ai_step": True,
                "_ai_step_modified": False
            })

            # Replace the modified step with the original
            self.ai_generated_steps[step_index] = original_step

            debug("Reset AI step to original state",
                  stage="state_management", operation="ai_step_reset",
                  context={'step_index': step_index, 'step_no': original_step.get('step_no')})
            return True

        return False

    def disable_hybrid_editing(self):
        """Disable hybrid editing mode and clear manual steps."""
        self.hybrid_editing_enabled = False
        self.step_insertion_points.clear()
        self.combined_step_table = None
        self.ai_generated_steps = None
        debug("Disabled hybrid editing mode",
              stage="state_management", operation="hybrid_editing")

    def add_manual_step(self, step: Dict[str, Any], insertion_point: str = "end"):
        """
        Add a manual step at the specified insertion point.

        Args:
            step: Manual step data
            insertion_point: Where to insert ("before_X", "after_X", "start", "end")
        """
        step["_is_manual"] = True
        step["_is_locked"] = False
        step["_insertion_point"] = insertion_point
        step["_created_at"] = datetime.now().isoformat()

        if insertion_point not in self.step_insertion_points:
            self.step_insertion_points[insertion_point] = []

        self.step_insertion_points[insertion_point].append(step)
        debug("Added manual step",
              stage="state_management", operation="hybrid_editing",
              context={'insertion_point': insertion_point})

    def remove_manual_step(self, step_id: str, insertion_point: str):
        """Remove a manual step from the specified insertion point."""
        if insertion_point in self.step_insertion_points:
            self.step_insertion_points[insertion_point] = [
                step for step in self.step_insertion_points[insertion_point]
                if step.get("_step_id") != step_id
            ]
            debug("Removed manual step",
                  stage="state_management", operation="hybrid_editing",
                  context={'step_id': step_id, 'insertion_point': insertion_point})

    def get_combined_steps(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get the combined AI and manual steps.

        Returns:
            List of combined steps or None if hybrid editing is not enabled
        """
        if not self.hybrid_editing_enabled or not self.ai_generated_steps:
            return None

        # If no manual steps have been added, return AI steps directly
        if not self.step_insertion_points:
            return self.ai_generated_steps.copy()

        from core.step_merger import merge_ai_and_manual_steps

        merged_steps, _, _ = merge_ai_and_manual_steps(
            self.ai_generated_steps,
            self.step_insertion_points
        )

        return merged_steps

    def validate_step_data_consistency(self, stage_name: str = "Unknown") -> Dict[str, Any]:
        """
        Validate that step data is consistent across different sources for hybrid editing debugging.

        Args:
            stage_name: Name of the stage calling this validation (for logging)

        Returns:
            Dict containing validation results and recommendations
        """
        debug(f"Step Data Consistency Validation started",
              stage="data_validation",
              operation="validation_check",
              context={"stage_name": stage_name})

        validation_results = {
            "stage": stage_name,
            "hybrid_editing_active": False,
            "data_sources_consistent": True,
            "issues": [],
            "recommendations": [],
            "step_data_summary": {}
        }

        # Check if hybrid editing is active
        validation_results["hybrid_editing_active"] = (
            self.hybrid_editing_enabled and
            bool(self.step_insertion_points or self.combined_step_table)
        )

        # Get current step data
        selected_step = getattr(self, 'selected_step', None)
        selected_step_table_entry = getattr(self, 'selected_step_table_entry', None)

        if selected_step and selected_step_table_entry:
            step_no = selected_step.get('Step No')
            original_action = selected_step.get('Test Steps', '')
            step_table_action = selected_step_table_entry.get('action', '')

            validation_results["step_data_summary"] = {
                "step_no": step_no,
                "original_action": original_action,
                "step_table_action": step_table_action,
                "actions_match": original_action.lower() == step_table_action.lower(),
                "step_is_synthetic": selected_step.get('_is_synthetic', False),
                "step_is_manual": selected_step_table_entry.get('_is_manual', False),
                "step_is_ai_generated": selected_step_table_entry.get('_is_ai_generated', False)
            }

            # Check for action mismatches (informational only - step table is authoritative)
            if not validation_results["step_data_summary"]["actions_match"]:
                # Note: This is informational only since step_table_entry is the authoritative source
                validation_results["issues"].append(
                    f"Data synchronization note: Original '{original_action}' differs from Step Table '{step_table_action}'"
                )

                # Always recommend using step table as authoritative source
                validation_results["recommendations"].append(
                    "Step table entry is the authoritative source for script generation (working as designed)"
                )

                # Log that this is expected behavior when hybrid editing is active
                debug(f"Step table entry used as authoritative source (original: '{original_action}' → step table: '{step_table_action}')",
                      stage="data_validation", operation="validation_check",
                      context={"step_no": validation_results["step_data_summary"]["step_no"],
                              "hybrid_editing": validation_results["hybrid_editing_active"]})

        # Check effective step table consistency
        effective_steps = self.get_effective_step_table()
        original_steps = self.step_table_json or []

        if len(effective_steps) != len(original_steps):
            validation_results["issues"].append(
                f"Step count mismatch: Effective {len(effective_steps)} vs Original {len(original_steps)}"
            )
            validation_results["recommendations"].append(
                "Step count difference indicates hybrid editing - verify all stages use get_effective_step_table()"
            )

        # Log validation results only if issues found
        if validation_results["issues"] or not validation_results["data_sources_consistent"]:
            debug(f"Step data validation completed",
                  stage="state_management", operation="validation_warning",
                  context={'stage_name': stage_name,
                          'hybrid_editing_active': validation_results['hybrid_editing_active'],
                          'data_sources_consistent': validation_results['data_sources_consistent'],
                          'issues_count': len(validation_results['issues']),
                          'issues': validation_results['issues'],
                          'recommendations': validation_results['recommendations']})

        return validation_results

    def sync_step_table_with_combined(self):
        """
        Synchronize the main step table with combined steps if hybrid editing is active.
        This ensures all stages use the combined steps.
        """
        if self.hybrid_editing_enabled:
            # Get the latest combined steps (even if no manual steps added yet)
            combined_steps = self.get_combined_steps()

            # If no combined steps but we have AI steps, use AI steps directly
            if not combined_steps and self.ai_generated_steps:
                combined_steps = self.ai_generated_steps.copy()
                debug("Using AI steps directly as no manual steps added yet",
                      stage="state_management", operation="hybrid_editing",
                      context={'ai_steps_count': len(combined_steps)})

            if combined_steps:
                old_step_count = len(self.step_table_json) if self.step_table_json else 0
                new_step_count = len(combined_steps)

                # Update main step table
                self.step_table_json = combined_steps
                self.combined_step_table = combined_steps

                # Update total steps
                old_total = self.total_steps
                self.total_steps = len(combined_steps)

                # Generate updated markdown using the combined steps directly
                from core.step_merger import StepMerger
                merger = StepMerger()
                self.step_table_markdown = merger.generate_markdown_table(combined_steps)

                debug("Synchronized step table with combined steps",
                      stage="state_management", operation="hybrid_editing",
                      context={'old_step_count': old_step_count, 'new_step_count': new_step_count,
                              'old_total': old_total, 'new_total': self.total_steps})

                # Save to persistent JSON storage
                self.save_step_data_to_json(combined_steps, {
                    'source': 'hybrid_editing_sync',
                    'ai_steps_count': len(self.ai_generated_steps or []),
                    'manual_steps_count': len(combined_steps) - len(self.ai_generated_steps or []),
                    'sync_timestamp': datetime.now().isoformat()
                })
