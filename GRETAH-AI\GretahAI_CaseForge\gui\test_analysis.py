#!/usr/bin/env python3

import streamlit as st
import pandas as pd
import os
from datetime import datetime, timedelta
import sqlite3
import db_helper as db
from pathlib import Path
from helpers import create_formatted_excel_from_scenarios, export_test_cases_to_csv, format_csv_for_external_tools

# Import visualization and reporting modules
from gui.visualization import render_visualization
from gui.reporting import render_reporting

def render_test_analysis():
    """
    Render the test analysis page.
    """
    # Test Analysis Page
    st.markdown('<h1 class="main-header">📊 Test Analysis Dashboard</h1>', unsafe_allow_html=True)

    # Check if the user is already logged in through the admin panel
    is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
    current_user = st.session_state.get("admin_username", "")

    # If the user is logged in as admin, use that username
    if is_admin_logged_in and not current_user:
        current_user = st.session_state.get("admin_user", "")
        # Store it in admin_username for consistency
        st.session_state["admin_username"] = current_user

    # If no user is logged in, show a message
    if not current_user:
        st.warning("Please log in through the Admin Panel to view your test runs.")

        # Add a button to navigate to the Admin Panel
        if st.button("Go to Admin Panel", key="test_analysis_admin_panel_button"):
            # Set a flag to automatically open the Admin Panel expander
            st.session_state["open_admin_panel"] = True
            # Remove unnecessary rerun - let natural flow handle update
    else:
        # Show the user's test runs with a clear message
        st.info(f"Showing test runs for user: {current_user}")

        # Create tabs for different analysis functions
        analysis_tabs = st.tabs(["Test Runs", "Visualization", "Reporting"])

        # We'll handle the unwanted sections differently - by not rendering them at all

        # Tab 1: Test Runs - For viewing and editing test cases
        with analysis_tabs[0]:
            # Get the user's test runs from the database
            try:
                test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
                if test_runs is None:
                    test_runs = []
                    
            except Exception as e:
                st.error(f"Error retrieving test runs: {str(e)}")
                test_runs = []

            # Check if we actually have test runs data
            if test_runs and len(test_runs) > 0:
                # Create a DataFrame from the test runs
                test_runs_df = pd.DataFrame(test_runs)

                # Format the timestamp column
                if "timestamp" in test_runs_df.columns:
                    test_runs_df["timestamp"] = pd.to_datetime(test_runs_df["timestamp"])
                    test_runs_df["timestamp"] = test_runs_df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S")

                # Rename columns for better display
                test_runs_df = test_runs_df.rename(columns={
                    "id": "Run ID",
                    "jira_id": "JIRA ID",
                    "test_type": "Test Type",
                    "timestamp": "Timestamp",
                    "num_test_cases": "# Test Cases",
                    "status": "Status",
                    "notes": "Notes",
                    "user_name": "User Name"  # Make sure this mapping exists
                })

                # Check if test runs are selected (from session state)
                selected_run_ids = st.session_state.get("selected_test_run_ids", [])

                # If no runs are selected, show the list of test runs
                if not selected_run_ids:
                    # Create a more visually appealing display for test runs
                    st.markdown("### Your Test Runs")

                    # Create a table with action buttons
                    # First, create a copy of the dataframe for display
                    display_runs_df = test_runs_df.copy()

                    # Add a view button column
                    display_runs_df["View"] = False

                    # Display the table with action buttons
                    edited_runs = st.data_editor(
                        display_runs_df,
                        column_config={
                            "Run ID": st.column_config.NumberColumn("Run ID", width="small"),
                            "JIRA ID": st.column_config.TextColumn("JIRA ID", width="small"),
                            "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                            "Timestamp": st.column_config.DatetimeColumn("Created", width="medium", format="DD-MM-YYYY HH:mm"),
                            "# Test Cases": st.column_config.NumberColumn("# Test Cases", width="small"),
                            "Status": st.column_config.TextColumn("Status", width="small"),
                            "Notes": st.column_config.TextColumn("Notes", width="medium"),
                            "User Name": st.column_config.TextColumn("User Name", width="small", disabled=True),
                            "View": st.column_config.CheckboxColumn("View", width="small", help="Select to view test cases")
                        },
                        disabled=["Run ID", "JIRA ID", "Test Type", "Timestamp", "# Test Cases", "Status", "Notes", "User Name"],
                        hide_index=True,
                        use_container_width=True,
                        key="test_runs_table"
                    )

                    # Check if any runs are selected for viewing
                    if edited_runs["View"].any():
                        # Get all selected runs
                        selected_rows = edited_runs[edited_runs["View"]]
                        # Store the selected run IDs in session state
                        selected_run_ids = selected_rows["Run ID"].tolist()
                        st.session_state["selected_test_run_ids"] = selected_run_ids
                        # Rerun to show the selected data immediately
                        st.rerun()

                # If runs are selected, show their test cases
                if selected_run_ids:
                    # Show a back button to return to the test run list
                    if st.button("← Back to Test Runs", key="back_to_test_runs"):
                        st.session_state.pop("selected_test_run_ids", None)
                        st.rerun()
                    
                    # Get the test cases for the selected test runs
                    all_test_cases = []  # Will store all test cases from all selected runs
                        
                    
                    # Get the test cases for the selected test runs
                    all_test_cases = []  # Will store all test cases from all selected runs
                    
                    # Process each selected test run
                    for selected_run_id in selected_run_ids:
                        # Always try to get test steps directly from the database
                        try:
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            if not run_info.empty:
                                jira_id = run_info["JIRA ID"].iloc[0]
                                test_type = run_info["Test Type"].iloc[0]

                                # Convert selected_run_id to regular Python int to avoid numpy.int64 issues
                                selected_run_id = int(selected_run_id)

                                # Get test cases with steps for this test run
                                conn = sqlite3.connect(db.DATABASE_PATH, timeout=60)
                                conn.row_factory = sqlite3.Row
                                cursor = conn.cursor()

                                # Simple query to get test cases
                                simple_query = """
                                SELECT
                                    tc.id as test_case_db_id,
                                    tc.jira_id as "User Story ID",
                                    tc.test_case_id as "Test Case ID",
                                    tc.test_case_objective as "Test Case Objective",
                                    tc.prerequisite as "Prerequisite",
                                    tc.priority as "Priority",
                                    tc.test_type as "Test Type",
                                    tc.test_group as "Test Group",
                                    tc.project as "Project",
                                    tc.feature as "Feature",
                                    tc.timestamp as "Timestamp",
                                    tc.dashboard_test_type as "Dashboard Test Type",
                                    tc.user_name as "User Name",
                                    tc.is_edited as "Is Edited",
                                    tc.test_run_id as "Test Run ID"
                                FROM test_cases tc
                                WHERE tc.test_run_id = ?
                                ORDER BY tc.test_case_id
                                """
                                
                                cursor.execute(simple_query, (selected_run_id,))
                                simple_rows = cursor.fetchall()
                                
                                if len(simple_rows) > 0:
                                    # Now get the test steps
                                    test_case_ids = [row['test_case_db_id'] for row in simple_rows]
                                    if test_case_ids:
                                        placeholders = ','.join(['?' for _ in test_case_ids])
                                        steps_query = f"""
                                        SELECT test_case_id, step_number, test_step, expected_result, 
                                               actual_result, test_status, defect_id, comments
                                        FROM test_steps 
                                        WHERE test_case_id IN ({placeholders})
                                        ORDER BY test_case_id, step_number
                                        """
                                        cursor.execute(steps_query, test_case_ids)
                                        steps_rows = cursor.fetchall()
                                        
                                        # Convert to the format expected by the rest of the code
                                        rows = []
                                        for test_case in simple_rows:
                                            # Get steps for this test case
                                            case_steps = [s for s in steps_rows if s['test_case_id'] == test_case['test_case_db_id']]
                                            
                                            if case_steps:
                                                # First step goes with the test case header
                                                first_step = case_steps[0]
                                                case_dict = dict(test_case)
                                                case_dict["Step No"] = first_step['step_number']
                                                case_dict["Test Steps"] = first_step['test_step']
                                                case_dict["Expected Result"] = first_step['expected_result']
                                                case_dict["Actual Result"] = first_step['actual_result'] or ""
                                                case_dict["Test Status"] = first_step['test_status'] or ""
                                                case_dict["Defect ID"] = first_step['defect_id'] or ""
                                                case_dict["Comments"] = first_step['comments'] or ""
                                                rows.append(case_dict)
                                                
                                                # Add remaining steps as separate rows
                                                for step in case_steps[1:]:
                                                    step_dict = dict(test_case)
                                                    # Clear test case metadata for subsequent steps
                                                    step_dict["Timestamp"] = ""
                                                    step_dict["Project"] = ""
                                                    step_dict["Feature"] = ""
                                                    step_dict["User Story ID"] = ""
                                                    step_dict["Test Case ID"] = ""
                                                    step_dict["Test Case Objective"] = ""
                                                    step_dict["Prerequisite"] = ""
                                                    step_dict["Priority"] = ""
                                                    step_dict["Test Type"] = ""
                                                    step_dict["Test Group"] = ""
                                                    step_dict["User Name"] = ""
                                                    step_dict["Dashboard Test Type"] = ""
                                                    step_dict["Is Edited"] = ""
                                                    # Add step data
                                                    step_dict["Step No"] = step['step_number']
                                                    step_dict["Test Steps"] = step['test_step']
                                                    step_dict["Expected Result"] = step['expected_result']
                                                    step_dict["Actual Result"] = step['actual_result'] or ""
                                                    step_dict["Test Status"] = step['test_status'] or ""
                                                    step_dict["Defect ID"] = step['defect_id'] or ""
                                                    step_dict["Comments"] = step['comments'] or ""
                                                    rows.append(step_dict)
                                            else:
                                                # No steps, create row with empty step data
                                                case_dict = dict(test_case)
                                                case_dict["Step No"] = ""
                                                case_dict["Test Steps"] = ""
                                                case_dict["Expected Result"] = ""
                                                case_dict["Actual Result"] = ""
                                                case_dict["Test Status"] = ""
                                                case_dict["Defect ID"] = ""
                                                case_dict["Comments"] = ""
                                                rows.append(case_dict)
                                    
                                    # Add this run's test cases to the overall list
                                    all_test_cases.extend(rows)
                                
                                # Close the database connection for this run
                                conn.close()
                            else:
                                st.error(f"Could not find test run {selected_run_id} in the database")
                        except Exception as e:
                            st.error(f"Error getting test steps for run {selected_run_id}: {e}")
                    
                    # Convert all collected test cases to DataFrame format
                    test_cases_with_steps = []

                    for row in all_test_cases:
                        if isinstance(row, dict):
                            test_case = row
                        else:
                            test_case = dict(row)
                            
                        # Create the row with proper structure
                        formatted_row = {
                            "Timestamp": test_case.get("Timestamp", ""),
                            "Project": test_case.get("Project", ""),
                            "Feature": test_case.get("Feature", ""),
                            "User Story ID": test_case.get("User Story ID", ""),
                            "Test Case ID": test_case.get("Test Case ID", ""),
                            "Test Case Objective": test_case.get("Test Case Objective", ""),
                            "Prerequisite": test_case.get("Prerequisite", ""),
                            "Step No": str(test_case.get("Step No", "")),
                            "Test Steps": test_case.get("Test Steps", ""),
                            "Expected Result": test_case.get("Expected Result", ""),
                            "Actual Result": test_case.get("Actual Result", ""),
                            "Test Status": test_case.get("Test Status", ""),
                            "Priority": test_case.get("Priority", ""),
                            "Defect ID": test_case.get("Defect ID", ""),
                            "Comments": test_case.get("Comments", ""),
                            "Test Type": test_case.get("Test Type", ""),
                            "Test Group": test_case.get("Test Group", ""),
                            "User Name": test_case.get("User Name", ""),
                            "Dashboard Test Type": test_case.get("Dashboard Test Type", ""),
                            "Test Run ID": test_case.get("Test Run ID", ""),
                            "Is Edited": test_case.get("Is Edited", "")
                        }
                        test_cases_with_steps.append(formatted_row)
                    # Convert to DataFrame
                    if test_cases_with_steps:
                        test_cases_df = pd.DataFrame(test_cases_with_steps)
                        
                        # Show summary of selected runs
                        if len(selected_run_ids) == 1:
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_ids[0]]
                            jira_id = run_info["JIRA ID"].iloc[0] if not run_info.empty else "Unknown"
                            test_type = run_info["Test Type"].iloc[0] if not run_info.empty else "Unknown"
                            
                            # Count actual test cases (not steps)
                            unique_test_case_count = len([tc for tc in test_cases_with_steps if tc.get("Test Case ID", "").strip() != ""])
                            

                            st.success(f"Found {unique_test_case_count} test cases for {selected_run_ids[0]}")
                            
                            # Display the test cases with a nicer header
                            st.markdown(f"""
                            <div style="background-color: #f0f2f6; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                                <h2 style="margin: 0; color: #1E88E5;">Test Cases for {jira_id}</h2>
                                <p style="margin: 5px 0 0 0;">Run ID: {selected_run_ids[0]} | Type: {test_type.upper()}</p>
                            </div>
                            """, unsafe_allow_html=True)
                        else:
                            # Count actual test cases (not steps) for multiple runs
                            unique_test_case_count = len([tc for tc in test_cases_with_steps if tc.get("Test Case ID", "").strip() != ""])
                            total_rows = len(test_cases_with_steps)
                            

                            st.success(f"Found {unique_test_case_count} test cases ({total_rows} total rows including steps) from {len(selected_run_ids)} test runs")
                            
                            # Display combined header for multiple runs
                            run_ids_str = ", ".join([str(rid) for rid in selected_run_ids])
                            st.markdown(f"""
                            <div style="background-color: #f0f2f6; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                                <h2 style="margin: 0; color: #1E88E5;">Combined Test Cases from Multiple Runs</h2>
                                <p style="margin: 5px 0 0 0;">Run IDs: {run_ids_str}</p>
                            </div>
                            """, unsafe_allow_html=True)
                    else:
                        st.warning(f"No test cases found for the selected test runs: {selected_run_ids}")
                        test_cases_df = pd.DataFrame()

                    
                    # Only proceed with display if we have test cases
                    if not test_cases_df.empty:
                        # Ensure User Story ID and Step No are treated as strings
                        if "User Story ID" in test_cases_df.columns:
                            test_cases_df["User Story ID"] = test_cases_df["User Story ID"].fillna("").astype(str)
                        if "Step No" in test_cases_df.columns:
                            test_cases_df["Step No"] = test_cases_df["Step No"].astype(str)

                        # REMOVE THIS BLOCK (the old/original metrics calculation and display)
                        # unique_test_cases = []
                        # if "Test Case ID" in test_cases_df.columns:
                        #     valid_test_case_ids = test_cases_df['Test Case ID'].dropna().astype(str)
                        #     valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                        #     valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                        #     unique_test_cases = valid_test_case_ids.unique()
                        #     if len(unique_test_cases) == 0:
                        #         unique_test_cases = test_cases_df['Test Case ID'].dropna().unique()
                        #
                        # status_counts = {"Pass": 0, "Fail": 0, "Blocked": 0, "Not Run": 0}
                        # if "Test Status" in test_cases_df.columns:
                        #     test_cases_df["Test Status"] = test_cases_df["Test Status"].fillna("").str.strip().str.upper()
                        #     for status in status_counts.keys():
                        #         status_counts[status] = len(test_cases_df[test_cases_df["Test Status"].str.upper() == status.upper()])
                        #
                        # priority_counts = {"High": 0, "Medium": 0, "Low": 0}
                        # if "Priority" in test_cases_df.columns:
                        #     test_cases_df["Priority"] = test_cases_df["Priority"].fillna("").str.strip().str.title()
                        #     for priority in priority_counts.keys():
                        #         priority_counts[priority] = len(test_cases_df[test_cases_df["Priority"].str.title() == priority])
                        #
                        # st.markdown("#### Test Run Summary")
                        # col1, col2, col3, col4 = st.columns(4)
                        # with col1:
                        #     st.metric("Total Test Cases", len(unique_test_cases))
                        # with col2:
                        #     st.metric("Passed", status_counts["Pass"],
                        #              delta=f"{status_counts['Pass']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                        #              delta_color="normal")
                        # with col3:
                        #     st.metric("Failed", status_counts["Fail"],
                        #              delta=f"{status_counts['Fail']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                        #              delta_color="inverse")
                        # with col4:
                        #     st.metric("Not Run", status_counts["Not Run"] + status_counts["Blocked"],
                        #              delta=f"{status_counts['Not Run'] + status_counts['Blocked']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                        #              delta_color="off")
                        #
                        # st.markdown("#### Test Case Priorities")
                        # col1, col2, col3 = st.columns(3)
                        # with col1:
                        #     st.metric("High Priority", priority_counts["High"],
                        #              delta=f"{priority_counts['High']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                        #              delta_color="inverse")
                        # with col2:
                        #     st.metric("Medium Priority", priority_counts["Medium"],
                        #              delta=f"{priority_counts['Medium']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                        #              delta_color="normal")
                        # with col3:
                        #     st.metric("Low Priority", priority_counts["Low"],
                        #              delta=f"{priority_counts['Low']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                        #              delta_color="normal")
                        # END REMOVE

                        # Get unique test case IDs and count
                        unique_test_cases = []
                        if "Test Case ID" in test_cases_df.columns:
                            valid_test_case_ids = test_cases_df['Test Case ID'].dropna().astype(str)
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                            unique_test_cases = valid_test_case_ids.unique()
                            if len(unique_test_cases) == 0:
                                unique_test_cases = test_cases_df['Test Case ID'].dropna().unique()

                                                # Updated column configuration to match the desired order with Timestamp as first column
                        # Configure column editability - only specific columns should be editable
                        editable_columns = ["Feature", "Test Case Objective", "Prerequisite", "Test Steps", "Expected Result", "Test Status", "Priority", "Actual Result", "Defect ID", "Comments"]
                        
                        column_config = {
                            "Timestamp": st.column_config.TextColumn(width="medium", disabled=True),  # Read-only
                            "Project": st.column_config.TextColumn(width="small", disabled=True),    # Read-only
                            "Feature": st.column_config.TextColumn(width="medium", disabled=False),  # Editable
                            "User Story ID": st.column_config.TextColumn(width="small", disabled=True),  # Read-only
                            "Test Case ID": st.column_config.TextColumn(width="small", disabled=True),  # Read-only
                            "Test Case Objective": st.column_config.TextColumn(width="medium", disabled=False),  # Editable
                            "Prerequisite": st.column_config.TextColumn(width="medium", disabled=False),  # Editable
                            "Step No": st.column_config.TextColumn(width="small", disabled=True),  # Read-only
                            "Test Steps": st.column_config.TextColumn(width="large", disabled=False),  # Editable
                            "Expected Result": st.column_config.TextColumn(width="large", disabled=False),  # Editable
                            "Actual Result": st.column_config.TextColumn(width="small", default="", disabled=False),  # Editable
                            "Test Status": st.column_config.SelectboxColumn(width="small", options=["PASS", "FAIL", "BLOCKED", "NOT RUN"], default="NOT RUN", disabled=False),  # Editable
                            "Priority": st.column_config.SelectboxColumn(width="small", options=["High", "Medium", "Low"], default="Medium", disabled=False),  # Editable
                            "Defect ID": st.column_config.TextColumn(width="small", default="", disabled=False),  # Editable
                            "Comments": st.column_config.TextColumn(width="large", default="", disabled=False),  # Editable
                            "Test Type": st.column_config.TextColumn(width="small", disabled=True),    # Read-only
                            "Test Group": st.column_config.TextColumn(width="medium", disabled=True),  # Read-only
                            "User Name": st.column_config.TextColumn(width="small", disabled=True),    # Read-only
                            "Dashboard Test Type": st.column_config.TextColumn(width="small", disabled=True)  # Read-only
                        }

                        # Ensure all columns in config exist in df
                        for col in column_config.keys():
                            if col not in test_cases_df.columns:
                                test_cases_df[col] = "" # Add missing column with default value

                        # Reorder columns based on config
                        ordered_columns = [col for col in column_config.keys() if col in test_cases_df.columns]
                        ordered_columns += [col for col in test_cases_df.columns if col not in ordered_columns]
                        test_cases_df = test_cases_df[ordered_columns]

                        # Process the dataframe to group test steps with their parent test cases
                        # This creates a more readable format similar to the Generated Test Cases tab

                        # Keep the original format that was working - don't consolidate
                        display_df = test_cases_df.copy()

                        # Store the original DataFrame before any modifications
                        if "original_test_analysis_df" not in st.session_state:
                            st.session_state["original_test_analysis_df"] = display_df.copy()

                        # Initialize editor data if not present
                        if "test_analysis_editor_data" not in st.session_state:
                            st.session_state["test_analysis_editor_data"] = display_df.copy()

                        # Determine which data to display
                        display_data = st.session_state["test_analysis_editor_data"]

                        # --- MOVE METRICS CALCULATION/DISPLAY TO HERE ---
                        # Get unique test case IDs and count
                        unique_test_cases = []
                        if "Test Case ID" in display_data.columns:
                            valid_test_case_ids = display_data['Test Case ID'].dropna().astype(str)
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                            unique_test_cases = valid_test_case_ids.unique()
                            if len(unique_test_cases) == 0:
                                unique_test_cases = display_data['Test Case ID'].dropna().unique()

                        # Count test statuses
                        status_counts = {"Pass": 0, "Fail": 0, "Blocked": 0, "Not Run": 0}
                        if "Test Status" in display_data.columns:
                            display_data["Test Status"] = display_data["Test Status"].fillna("").str.strip().str.upper()
                            for status in status_counts.keys():
                                status_counts[status] = len(display_data[display_data["Test Status"].str.upper() == status.upper()])

                        # Count priorities
                        priority_counts = {"High": 0, "Medium": 0, "Low": 0}
                        if "Priority" in display_data.columns:
                            display_data["Priority"] = display_data["Priority"].fillna("").str.strip().str.title()
                            for priority in priority_counts.keys():
                                priority_counts[priority] = len(display_data[display_data["Priority"].str.title() == priority])

                        # Create a summary section with metrics
                        st.markdown("#### Test Run Summary")
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Total Test Cases", len(unique_test_cases))
                        with col2:
                            st.metric("Passed", status_counts["Pass"],
                                     delta=f"{status_counts['Pass']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                     delta_color="normal")
                        with col3:
                            st.metric("Failed", status_counts["Fail"],
                                     delta=f"{status_counts['Fail']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                     delta_color="inverse")
                        with col4:
                            st.metric("Not Run", status_counts["Not Run"] + status_counts["Blocked"],
                                     delta=f"{status_counts['Not Run'] + status_counts['Blocked']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                     delta_color="off")

                        st.markdown("#### Test Case Priorities")
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("High Priority", priority_counts["High"],
                                     delta=f"{priority_counts['High']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                     delta_color="inverse")
                        with col2:
                            st.metric("Medium Priority", priority_counts["Medium"],
                                     delta=f"{priority_counts['Medium']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                     delta_color="normal")
                        with col3:
                            st.metric("Low Priority", priority_counts["Low"],
                                     delta=f"{priority_counts['Low']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                     delta_color="normal")
                        # --- END MOVE ---

                        # Configure column editability - only specific columns should be editable
                        editable_columns = ["Feature", "Test Case Objective", "Prerequisite", "Test Steps", "Expected Result", "Test Status", "Priority", "Actual Result", "Defect ID", "Comments"]
                        
                        column_config = {
                            "Timestamp": st.column_config.TextColumn(width="medium", disabled=True),  # Read-only
                            "Project": st.column_config.TextColumn(width="small", disabled=True),    # Read-only
                            "Feature": st.column_config.TextColumn(width="medium", disabled=False),  # Editable
                            "User Story ID": st.column_config.TextColumn(width="small", disabled=True),  # Read-only
                            "Test Case ID": st.column_config.TextColumn(width="small", disabled=True),  # Read-only
                            "Test Case Objective": st.column_config.TextColumn(width="medium", disabled=False),  # Editable
                            "Prerequisite": st.column_config.TextColumn(width="medium", disabled=False),  # Editable
                            "Step No": st.column_config.TextColumn(width="small", disabled=True),  # Read-only
                            "Test Steps": st.column_config.TextColumn(width="large", disabled=False),  # Editable
                            "Expected Result": st.column_config.TextColumn(width="large", disabled=False),  # Editable
                            "Actual Result": st.column_config.TextColumn(width="small", default="", disabled=False),  # Editable
                            "Test Status": st.column_config.SelectboxColumn(width="small", options=["PASS", "FAIL", "BLOCKED", "NOT RUN"], default="NOT RUN", disabled=False),  # Editable
                            "Priority": st.column_config.SelectboxColumn(width="small", options=["High", "Medium", "Low"], default="Medium", disabled=False),  # Editable
                            "Defect ID": st.column_config.TextColumn(width="small", default="", disabled=False),  # Editable
                            "Comments": st.column_config.TextColumn(width="large", default="", disabled=False),  # Editable
                            "Test Type": st.column_config.TextColumn(width="small", disabled=True),    # Read-only
                            "Test Group": st.column_config.TextColumn(width="medium", disabled=True),  # Read-only
                            "User Name": st.column_config.TextColumn(width="small", disabled=True),    # Read-only
                            "Dashboard Test Type": st.column_config.TextColumn(width="small", disabled=True)  # Read-only
                        }

                        # Ensure all columns in config exist in df
                        for col in column_config.keys():
                            if col not in test_cases_df.columns:
                                test_cases_df[col] = "" # Add missing column with default value

                        # Reorder columns based on config
                        ordered_columns = [col for col in column_config.keys() if col in test_cases_df.columns]
                        ordered_columns += [col for col in test_cases_df.columns if col not in ordered_columns]
                        test_cases_df = test_cases_df[ordered_columns]

                        # Process the dataframe to group test steps with their parent test cases
                        # This creates a more readable format similar to the Generated Test Cases tab

                        # Keep the original format that was working - don't consolidate
                        display_df = test_cases_df.copy()

                        # Store the original DataFrame before any modifications
                        if "original_test_analysis_df" not in st.session_state:
                            st.session_state["original_test_analysis_df"] = display_df.copy()

                        # Initialize editor data if not present
                        if "test_analysis_editor_data" not in st.session_state:
                            st.session_state["test_analysis_editor_data"] = display_df.copy()

                        # Add action buttons (same as other tabs)
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            if st.button("🔄 Update Table with AI Output", key="update_table_with_ai_output_analysis"):
                                if "ai_modified_df_test_analysis" in st.session_state and st.session_state["ai_modified_df_test_analysis"] is not None:
                                    st.session_state["test_analysis_editor_data"] = st.session_state["ai_modified_df_test_analysis"].copy()
                                    st.session_state["test_analysis_table_updated_with_ai"] = True
                                    st.success("Table updated with latest AI modifications.")
                                    st.rerun()
                                else:
                                    st.warning("No new AI modifications to apply.")

                        with col2:
                            if st.button("↩️ Undo All Modifications", key="undo_all_modifications_analysis"):
                                if "original_test_analysis_df" in st.session_state:
                                    st.session_state["test_analysis_editor_data"] = st.session_state["original_test_analysis_df"].copy()
                                    st.session_state["test_analysis_table_updated_with_ai"] = False
                                    if "ai_modified_df_test_analysis" in st.session_state:
                                        del st.session_state["ai_modified_df_test_analysis"]
                                    st.success("All modifications have been undone. Original data restored.")
                                    st.rerun()

                        # Determine which data to display
                        display_data = st.session_state["test_analysis_editor_data"]
                        
                        # Show status of current data
                        if st.session_state.get("test_analysis_table_updated_with_ai", False):
                            st.info("📊 Currently showing: AI-Modified Test Cases")
                        else:
                            st.info("📊 Currently showing: Original Test Cases")

                        # Create a data editor for the test cases with dynamic key
                        editor_key = f"test_cases_editor_{'ai' if st.session_state.get('test_analysis_table_updated_with_ai', False) else 'original'}"
                        edited_df = st.data_editor(
                            display_data,
                            column_config=column_config,
                            hide_index=True,
                            use_container_width=True,
                            key=editor_key
                        )

                        # Update session state if user made manual edits
                        if not edited_df.equals(display_data):
                            st.session_state["test_analysis_editor_data"] = edited_df.copy()

                        # Add a save button to update the database with edited data
                        if st.button("💾 Save Changes to Database", key="save_test_analysis_changes"):
                            with st.spinner("Saving changes to database..."):
                                try:
                                    # Check if there are changes
                                    original_df = st.session_state.get("original_test_analysis_df", display_df)
                                    if not edited_df.equals(original_df):
                                        # Get current user
                                        current_user = st.session_state.get("admin_username", "")
                                        
                                        # Get JIRA ID from the first non-empty User Story ID
                                        jira_id = None
                                        if "User Story ID" in edited_df.columns:
                                            user_story_ids = edited_df["User Story ID"].dropna().unique()
                                            if len(user_story_ids) > 0:
                                                jira_id = user_story_ids[0]
                                        
                                        if jira_id and current_user:
                                            # Update the database with the edited data
                                            conn = sqlite3.connect(db.DATABASE_PATH, timeout=60)
                                            cursor = conn.cursor()
                                            
                                            updated_count = 0
                                            for index, row in edited_df.iterrows():
                                                test_case_id = row.get("Test Case ID", "").strip()
                                                step_no = str(row.get("Step No", "")).strip()
                                                test_run_id = row.get("Test Run ID", "")
                                                
                                                if test_case_id and test_run_id:
                                                    if step_no and step_no != "":
                                                        # This is a step row - update test_steps table
                                                        cursor.execute("""
                                                            UPDATE test_steps 
                                                            SET test_step = ?, expected_result = ?, actual_result = ?, 
                                                                test_status = ?, defect_id = ?, comments = ?
                                                            WHERE test_case_id = (
                                                                SELECT id FROM test_cases 
                                                                WHERE test_case_id = ? AND test_run_id = ?
                                                            ) AND step_number = ?
                                                        """, (
                                                            row.get("Test Steps", ""),
                                                            row.get("Expected Result", ""),
                                                            row.get("Actual Result", ""),
                                                            row.get("Test Status", ""),
                                                            row.get("Defect ID", ""),
                                                            row.get("Comments", ""),
                                                            test_case_id,
                                                            test_run_id,
                                                            step_no
                                                        ))
                                                    
                                                    # ALWAYS update test_cases table for priority and other test case fields
                                                    # regardless of whether this is a step row or header row
                                                    cursor.execute("""
                                                        UPDATE test_cases 
                                                        SET feature = ?, test_case_objective = ?, prerequisite = ?, 
                                                            priority = ?, is_edited = 1
                                                        WHERE test_case_id = ? AND test_run_id = ?
                                                    """, (
                                                        row.get("Feature", ""),
                                                        row.get("Test Case Objective", ""),
                                                        row.get("Prerequisite", ""),
                                                        row.get("Priority", ""),
                                                        test_case_id,
                                                        test_run_id
                                                    ))
                                                    updated_count += 1
                                            
                                            conn.commit()
                                            conn.close()
                                            
                                            # Update session state with the saved data
                                            st.session_state["test_analysis_editor_data"] = edited_df.copy()
                                            st.session_state["original_test_analysis_df"] = edited_df.copy()
                                            
                                            st.success(f"✅ Database updated successfully! {updated_count} rows updated.")
                                            st.rerun()
                                        else:
                                            st.error("❌ Missing JIRA ID or user information for database update.")
                                    else:
                                        st.info("No changes detected.")
                                except Exception as e:
                                    st.error(f"Error saving test cases: {str(e)}")

                        # --- Move AI TestCase Modification expander below the table and save button ---
                        from helpers.ui.components import render_test_case_modification_interface
                        selected_model = st.session_state.get("selected_model_persistent", "gemini-2.0-flash")
                        google_api_key = st.session_state.get("google_api_key", "")
                        ai_modified_df = render_test_case_modification_interface(
                            test_cases_df=st.session_state.get("test_analysis_editor_data", display_df),
                            jira_issue=st.session_state.get("jira_issue"),
                            selected_model=selected_model,
                            google_api_key=google_api_key,
                            tab_key="test_analysis",
                            store_in_session=True
                        )
                        # --- End move ---

        # Tab 2: Visualization - For charts and graphs
        with analysis_tabs[1]:
            # Pass the current user to visualization which will handle its own data filtering
            # The visualization module will query the database directly based on user selection
            # of JIRA ID and Test Type filters
            render_visualization(current_user)

        # Tab 3: Reporting - For generating reports
        with analysis_tabs[2]:
            render_reporting(current_user)