#!/usr/bin/env python3

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import os
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import db_helper as db  # Import database helper

def render_visualization(current_user):
    """
    Render the visualization tab for test analysis.

    Args:
        current_user (str): The current user's username
    """
    st.subheader("Test Case Analytics Dashboard")
    st.info("Visualize your test case data to gain insights into your testing coverage and distribution.")

    # Check if the database exists
    if os.path.exists(db.DATABASE_PATH):
        # Get data from database
        try:
            # Get unique JIRA IDs
            jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

            # Get test runs
            try:
                test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
                if test_runs is None:
                    test_runs = []
            except Exception as e:
                st.error(f"Error retrieving test runs: {str(e)}")
                test_runs = []

            # If no data found, show a helpful message
            if not jira_ids and not test_runs:
                st.warning("No test data found for your user. Please generate some test cases first.")
                st.info("Go to the Test Generator tab to create test cases, then come back to this tab to visualize them.")
                return
        except Exception as e:
            st.error(f"Error testing database functions: {str(e)}")
            st.info("There might be an issue with the database. Please check the database connection and try again.")
            return
    else:
        st.error(f"Database not found at {db.DATABASE_PATH}")
        return

    # Create a selection interface for data source
    st.markdown("### 📊 Select Data Source")

    # Create tabs for different data sources
    data_source_tabs = st.tabs(["By JIRA ID", "By Test Run", "All Data"])

    # Initialize variables
    df_all = pd.DataFrame()
    selected_data_source = None
    selected_jira_id = None
    selected_run_id = None

    # Tab 1: By JIRA ID
    with data_source_tabs[0]:
        # Get unique JIRA IDs from the database
        jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

        if jira_ids:
            # Create a selectbox for JIRA IDs
            selected_jira_id = st.selectbox(
                "Select JIRA ID",
                options=jira_ids,
                key="viz_jira_id_select"
            )

            # Add a test type filter
            test_type_options = ["All Types", "positive", "negative", "security", "performance"]
            selected_test_type = st.selectbox(
                "Select Test Type",
                options=test_type_options,
                key="viz_test_type_select"
            )

            # Apply button
            if st.button("Show Visualizations for Selected JIRA ID", key="viz_jira_apply", use_container_width=True):
                with st.spinner("Loading data..."):
                    # Get test cases for the selected JIRA ID with proper filtering
                    if selected_test_type == "All Types":
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            test_type="all",
                            user_name=current_user
                        )
                    else:
                        # Get specific test type for this JIRA ID
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            test_type=selected_test_type,
                            user_name=current_user
                        )

                    if not test_cases_data.empty:
                        df_all = test_cases_data
                        selected_data_source = "jira"
                        
                        # Show success message with run information
                        if "Test Run ID" in df_all.columns:
                            unique_run_ids = df_all["Test Run ID"].dropna().unique()
                            st.success(f"Loaded {len(df_all)} rows from test runs: {unique_run_ids}")
                        else:
                            st.success(f"Loaded {len(df_all)} rows for JIRA ID: {selected_jira_id}")
                    else:
                        if selected_test_type == "All Types":
                            st.error(f"No test cases found for JIRA ID: {selected_jira_id} with test_type='all'")
                            st.info("Make sure you have test runs with test_type='all' in your database for this JIRA ID.")
                        else:
                            st.error(f"No test cases found for JIRA ID: {selected_jira_id} with test type: {selected_test_type}")
        else:
            st.warning("No JIRA IDs found in the database. Generate some test cases first.")

    # Tab 2: By Test Run
    with data_source_tabs[1]:
        # Get all test runs for the current user
        try:
            test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
            if test_runs is None:
                test_runs = []
        except Exception as e:
            st.error(f"Error retrieving test runs: {str(e)}")
            test_runs = []

        if test_runs:
            # Create a dataframe for better display
            test_runs_df = pd.DataFrame(test_runs)

            # Add a timestamp column if it exists
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['timestamp'] = pd.to_datetime(test_runs_df['timestamp'])
                test_runs_df = test_runs_df.sort_values('timestamp', ascending=False)

            # Format the dataframe for display
            display_runs = test_runs_df[['id', 'jira_id', 'test_type', 'timestamp', 'num_test_cases', 'status', 'notes']].copy()

            # Format the timestamp
            if 'timestamp' in display_runs.columns:
                display_runs['timestamp'] = pd.to_datetime(display_runs['timestamp']).dt.strftime("%b %d, %Y %H:%M")

            # Rename columns for better display
            display_runs.columns = ['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes']

            # Convert test type to uppercase
            display_runs['Test Type'] = display_runs['Test Type'].str.upper()

            # Add expander for the test runs table
            with st.expander("Expand to View Test Runs Table 📋", expanded=False):
                # Display the test runs with better formatting
                st.dataframe(
                    display_runs,
                    use_container_width=True,
                    column_config={
                        "Run ID": st.column_config.NumberColumn("Run ID", width="small"),
                        "JIRA ID": st.column_config.TextColumn("JIRA ID", width="small"),
                        "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                        "Timestamp": st.column_config.TextColumn("Created", width="medium"),
                        "# Test Cases": st.column_config.NumberColumn("# Test Cases", width="small"),
                        "Status": st.column_config.TextColumn("Status", width="small"),
                        "Notes": st.column_config.TextColumn("Notes", width="medium")
                    },
                    hide_index=True
                )

            # Format the timestamp for better readability
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['formatted_timestamp'] = pd.to_datetime(test_runs_df['timestamp']).dt.strftime("%b %d, %Y %H:%M")
            else:
                test_runs_df['formatted_timestamp'] = "Unknown"

            # Create a selectbox for test runs with better formatting
            run_options = [
                f"Run {run['id']}: {run['jira_id']} - {run['test_type'].upper()} ({run['formatted_timestamp']})"
                for _, run in test_runs_df.iterrows()
            ]

            selected_run_option = st.selectbox(
                "Select Test Run",
                options=run_options,
                key="viz_test_run_select"
            )

            # Extract the run ID from the selected option
            if selected_run_option:
                selected_run_id = int(selected_run_option.split(":")[0].replace("Run ", "").strip())

            # Apply button
            if st.button("Show Visualizations for Selected Test Run", key="viz_run_apply", use_container_width=True):
                with st.spinner("Loading data..."):
                    # Get test cases for the selected test run
                    test_cases_data = db.get_test_cases_by_test_run(db.DATABASE_PATH, selected_run_id)

                    if isinstance(test_cases_data, list) and len(test_cases_data) > 0:
                        df_all = pd.DataFrame(test_cases_data)
                        selected_data_source = "run"
                        st.success(f"Loaded {len(df_all)} test cases for Test Run ID: {selected_run_id}")
                    elif hasattr(test_cases_data, 'empty') and not test_cases_data.empty:
                        df_all = test_cases_data
                        selected_data_source = "run"
                        st.success(f"Loaded {len(df_all)} test cases for Test Run ID: {selected_run_id}")
                    else:
                        st.error(f"No test cases found for Test Run ID: {selected_run_id}")
        else:
            st.warning("No test runs found in the database. Generate some test cases first.")

    # Tab 3: All Data
    with data_source_tabs[2]:
        st.markdown("This view shows visualizations for all your test cases across all JIRA IDs and test runs.")

        # Add date range filter
        default_start_date = datetime.now() - timedelta(days=90)
        default_end_date = datetime.now()
        date_range = st.date_input(
            "Date Range",
            value=(default_start_date, default_end_date),
            key="viz_all_date_range"
        )

        # Apply button
        if st.button("Show Visualizations for All Data", key="viz_all_apply", use_container_width=True):
            with st.spinner("Loading all test case data..."):
                # Convert date range to string format
                start_date = date_range[0].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 0 else None
                end_date = date_range[1].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 1 else None

                # Get all test cases within the date range
                all_data = db.get_test_cases_by_filters(
                    db.DATABASE_PATH,
                    start_date=start_date,
                    end_date=end_date,
                    user_name=current_user
                )

                if not all_data.empty:
                    df_all = all_data
                    selected_data_source = "all"
                    st.success(f"Loaded {len(df_all)} test cases from {start_date} to {end_date}")
                else:
                    st.error("No test cases found for the selected date range.")

    # Add a divider
    st.markdown("---")

    # Check if we have data to visualize
    if not df_all.empty:
        # Display summary metrics at the top
        st.markdown("### 📊 Key Metrics")

        # Create metrics row
        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)

        # Calculate metrics with improved logic to count test cases, not steps
        try:
            # For total test cases - count unique test case IDs only from rows that have them
            if "Test Case ID" in df_all.columns:
                # Get rows where Test Case ID is not empty (these represent actual test cases)
                test_case_rows = df_all[
                    df_all["Test Case ID"].notna() & 
                    (df_all["Test Case ID"].astype(str).str.strip() != "") &
                    (df_all["Test Case ID"].astype(str) != "")
                ]
                
                # Count unique test case IDs (now includes run ID to avoid deduplication)
                unique_test_case_ids = test_case_rows["Test Case ID"].unique()
                total_test_cases = len(unique_test_case_ids)
                
                # Debug: Show breakdown by test run
                if "Test Run ID" in df_all.columns:
                    run_breakdown = test_case_rows.groupby("Test Run ID")["Test Case ID"].nunique()
                    st.write(f"Debug: Test cases per run: {dict(run_breakdown)}")
                
            else:
                total_test_cases = 0
                test_case_rows = pd.DataFrame()

            # For JIRA IDs - count unique JIRA IDs from test case level only (use correct column name)
            if "JIRA ID" in df_all.columns and total_test_cases > 0:
                # Group by test case ID and get the first JIRA ID for each test case
                test_case_jira_ids = test_case_rows.groupby("Test Case ID")["JIRA ID"].first()
                unique_jira_ids = len(test_case_jira_ids.dropna().unique())
            else:
                unique_jira_ids = 0

            # For test status metrics - use test case level data only
            if "Test Status" in df_all.columns and total_test_cases > 0:
                # Group by test case ID and get the first status for each test case
                test_case_statuses = test_case_rows.groupby("Test Case ID")["Test Status"].first()
                                
                # Count passed test cases (case-insensitive)
                pass_count = len(test_case_statuses[
                    test_case_statuses.str.upper().str.contains("PASS", na=False)
                ])
                
                # Calculate pass rate
                pass_rate = int((pass_count / total_test_cases) * 100) if total_test_cases > 0 else 0
                
            else:
                pass_count = 0
                pass_rate = 0

            # For priority metrics - use test case level data only
            if "Priority" in df_all.columns and total_test_cases > 0:
                # Group by test case ID and get the first priority for each test case
                test_case_priorities = test_case_rows.groupby("Test Case ID")["Priority"].first()
                                
                # Count high priority test cases (case-insensitive)
                high_priority = len(test_case_priorities[
                    test_case_priorities.str.upper().str.contains("HIGH", na=False)
                ])
                
            else:
                high_priority = 0

        except Exception as e:
            st.error(f"Error calculating metrics: {str(e)}")
            total_test_cases = 0
            unique_jira_ids = 0
            pass_count = 0
            pass_rate = 0
            high_priority = 0

        # Display metrics
        with metric_col1:
            st.metric("Total Test Cases", total_test_cases)

        with metric_col2:
            st.metric("Pass Rate", f"{pass_rate}%", delta=f"{pass_count}/{total_test_cases}" if total_test_cases > 0 else None)

        with metric_col3:
            st.metric("High Priority", high_priority)

        with metric_col4:
            st.metric("User Stories", unique_jira_ids)

        # Add context information
        if selected_data_source == "jira":
            st.info(f"Showing visualizations for JIRA ID: **{selected_jira_id}**")
        elif selected_data_source == "run":
            st.info(f"Showing visualizations for Test Run ID: **{selected_run_id}**")
        elif selected_data_source == "all":
            st.info(f"Showing visualizations for all test cases from **{date_range[0].strftime('%Y-%m-%d')}** to **{date_range[1].strftime('%Y-%m-%d')}**")

        # Create tabs for different visualizations
        viz_tabs = st.tabs(["Test Type Distribution", "Test Status", "Priority Distribution"])

        # Tab 1: Test Type Distribution - Fix to count test cases, not steps
        with viz_tabs[0]:
            st.markdown("### Test Case Distribution by Type")

            if "Test Type" in df_all.columns and total_test_cases > 0:
                try:
                    # Use only test case rows for counting
                    test_case_types = test_case_rows.groupby("Test Case ID")["Test Type"].first()
                    
                    # Count test cases by test type
                    test_type_counts = test_case_types.value_counts().reset_index()
                    test_type_counts.columns = ["Test Type", "Count"]
                    
                    # Clean and standardize test type values
                    test_type_counts["Test Type"] = test_type_counts["Test Type"].fillna("Unknown")

                except Exception as e:
                    st.error(f"Error processing test type data: {str(e)}")
                    test_type_counts = pd.DataFrame(columns=["Test Type", "Count"])

                # Define colors for test types
                type_colors = {
                    "positive": "#4CAF50",
                    "negative": "#F44336",
                    "security": "#2196F3",
                    "performance": "#FF9800",
                    "Unknown": "#9E9E9E"
                }

                # Create a pie chart
                fig = px.pie(
                    test_type_counts,
                    values="Count",
                    names="Test Type",
                    title="Test Cases by Type",
                    color="Test Type",
                    color_discrete_map=type_colors,
                    hole=0.4
                )

                # Add hover information and configure proper interaction
                fig.update_traces(
                    textposition='inside',
                    textinfo='percent+label',
                    hoverinfo='label+percent+value',
                    marker=dict(line=dict(color='#FFFFFF', width=2)),
                    hovertemplate='<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent}<extra></extra>'
                )

                # Improve layout with proper legend interaction for highlighting only
                fig.update_layout(
                    height=500,
                    legend_title_text="Test Types",
                    legend=dict(
                        orientation="h", 
                        yanchor="bottom", 
                        y=1.02, 
                        xanchor="right", 
                        x=1,
                        itemclick=False,  # Disable click interaction
                        itemdoubleclick=False,  # Disable double-click interaction
                        itemsizing="constant"  # Keep legend item size constant
                    ),
                    margin=dict(t=60, b=20, l=20, r=20)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)
                
            else:
                st.warning("No Test Type data available for visualization.")

        # Tab 2: Test Status
        with viz_tabs[1]:
            st.markdown("### Test Case Status Analysis")

            if "Test Status" in df_all.columns:
                try:
                    # Clean and standardize test status values
                    df_all["Test Status"] = df_all["Test Status"].fillna("Not Run")

                    # Count test cases by status
                    if isinstance(df_all["Test Status"], pd.Series):
                        status_counts = df_all["Test Status"].value_counts().reset_index()
                        status_counts.columns = ["Status", "Count"]
                    else:
                        # Manual counting if not a Series
                        status_count_dict = {}
                        for status in df_all["Test Status"]:
                            if pd.isna(status):
                                status = "Not Run"
                            status_count_dict[status] = status_count_dict.get(status, 0) + 1

                        # Convert to DataFrame
                        status_counts = pd.DataFrame({
                            "Status": list(status_count_dict.keys()),
                            "Count": list(status_count_dict.values())
                        })
                except Exception as e:
                    st.error(f"Error processing test status data: {str(e)}")
                    # Create an empty DataFrame with the right columns
                    status_counts = pd.DataFrame(columns=["Status", "Count"])

                # Define colors for test status
                status_colors = {
                    "Pass": "#4CAF50",
                    "Fail": "#F44336",
                    "Blocked": "#FF9800",
                    "Not Run": "#9E9E9E"
                }

                # Create a bar chart
                fig = px.bar(
                    status_counts,
                    x="Status",
                    y="Count",
                    color="Status",
                    color_discrete_map=status_colors,
                    title="Test Case Status Distribution"
                )

                # Improve layout
                fig.update_layout(
                    height=400,
                    xaxis_title="Test Status",
                    yaxis_title="Number of Test Cases",
                    legend_title_text="Status",
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

            else:
                st.warning("No Test Status data available for visualization.")

        # Tab 3: Priority Distribution
        with viz_tabs[2]:
            st.markdown("### Test Case Priority Distribution")

            if "Priority" in df_all.columns:
                # Clean and standardize priority values
                df_all["Priority"] = df_all["Priority"].fillna("Medium")

                # Count test cases by priority
                priority_counts = df_all["Priority"].value_counts().reset_index()
                priority_counts.columns = ["Priority", "Count"]

                # Define colors for priority levels
                priority_colors = {"High": "#F44336", "Medium": "#FF9800", "Low": "#4CAF50"}

                # Create a donut chart
                fig = px.pie(
                    priority_counts,
                    values="Count",
                    names="Priority",
                    title="Test Case Priority Distribution",
                    color="Priority",
                    color_discrete_map=priority_colors,
                    hole=0.6
                )

                # Add hover information
                fig.update_traces(
                    textposition='inside',
                    textinfo='percent+label',
                    hoverinfo='label+percent+value',
                    marker=dict(line=dict(color='#FFFFFF', width=2))
                )

                # Improve layout
                fig.update_layout(
                    height=500,
                    legend_title_text="Priority",
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                    margin=dict(t=60, b=20, l=20, r=20)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

                # Display priority counts as metrics
                col1, col2, col3 = st.columns(3)

                # Calculate percentages
                total = priority_counts["Count"].sum()

                with col1:
                    high_count = priority_counts[priority_counts["Priority"] == "High"]["Count"].sum() if "High" in priority_counts["Priority"].values else 0
                    high_pct = (high_count / total) * 100 if total > 0 else 0
                    st.metric("High Priority", high_count, f"{high_pct:.1f}%")

                with col2:
                    medium_count = priority_counts[priority_counts["Priority"] == "Medium"]["Count"].sum() if "Medium" in priority_counts["Priority"].values else 0
                    medium_pct = (medium_count / total) * 100 if total > 0 else 0
                    st.metric("Medium Priority", medium_count, f"{medium_pct:.1f}%")

                with col3:
                    low_count = priority_counts[priority_counts["Priority"] == "Low"]["Count"].sum() if "Low" in priority_counts["Priority"].values else 0
                    low_pct = (low_count / total) * 100 if total > 0 else 0
                    st.metric("Low Priority", low_count, f"{low_pct:.1f}%")


            else:
                st.warning("No Priority data available for visualization.")

