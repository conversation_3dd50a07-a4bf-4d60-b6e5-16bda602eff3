"""
GretahAI CaseForge - Test Case Database Helper Package

This package provides comprehensive database operations for managing test cases, test runs, 
JIRA issues, and test steps in the GretahAI CaseForge application. It serves as the primary 
data access layer for the test case management system.

The package is modularized for better maintainability while preserving all original functionality
and maintaining backward compatibility with existing code.

Key Features:
- SQLite database management with thread-safe operations
- Test case CRUD operations with versioning support
- Test run management and tracking
- JIRA issue integration and synchronization
- Database schema migration and validation
- Admin configuration management
- Excel export/import functionality
- Comprehensive error handling and retry mechanisms

Package Structure:
- core/: Database connection, schema management, and decorators
- models/: Data model operations (test_cases, test_runs, jira_issues, test_steps)
- operations/: Business logic operations (data retrieval, deletion, admin functions)
- utils/: Utility functions (export, backup, statistics)

Usage:
Import this package as a drop-in replacement for the original Test_case_db_helper.py:

    from db_helper import *
    # or
    import db_helper as db_helper

All original function names and signatures are preserved for backward compatibility.
"""

# Core functionality
from .core import (
    get_thread_local_connection,
    close_thread_local_connection,
    close_connection,
    DATABASE_PATH,
    retry_on_db_lock,
    migrate_database,
    init_db,
    backup_database,
    detect_schema_version,
    migrate_old_to_new_schema,
    verify_schema_completeness,
    standardize_schema,
    verify_and_update_schema,
    update_database_schema,
    update_existing_test_cases
)

# Admin operations
from .operations.admin_operations import (
    get_app_config,
    update_app_config,
    verify_admin_password,
    clear_database,
    clear_database_for_jira
)

# JIRA operations
from .models.jira_issues import (
    get_or_create_jira_issue,
    update_jira_issue_in_database,
    update_jira_issue_enhancement,
    get_jira_issue_enhancement,
    delete_jira_issue,
    delete_all_jira_issues,
    get_unique_jira_ids,
    get_enhanced_jira_description,
    save_enhanced_jira_description
)

# Test case operations
from .models.test_cases import (
    get_highest_test_case_id_number,
    save_test_cases_to_database,  # Direct import - no wrapper needed
    update_test_cases_in_database,
    delete_test_case,
    add_single_test_case,
    add_test_step_to_case,
    delete_test_step
)

# Test run operations
from .models.test_runs import (
    get_latest_test_run,
    create_test_run,
    update_test_run,
    get_test_runs_by_user,
    get_latest_test_run_id,
    get_or_create_test_run,
    update_test_run_completion
)

# Data operations
from .operations.data_operations import (
    get_test_cases_from_database,
    get_latest_generated_test_cases,
    get_all_test_cases_for_user,
    get_test_cases_by_test_run,
    get_test_cases_for_test_run,
    get_latest_generated_test_cases_original,
    fix_all_test_type,
    get_test_cases_by_filters,
    get_unified_filtered_test_cases,
    get_filter_options,
    get_latest_test_cases,
    get_most_recent_test_cases_exact,
    process_backup_style_test_data,
    count_test_cases_in_database_output,
    _get_all_test_cases_for_jira_id,
    _get_test_cases_by_jira_and_type
)

# Delete operations
from .operations.delete_operations import (
    delete_test_run,
    delete_edited_test_cases,
    delete_all_edited_test_cases,
    delete_specific_test_cases,
    delete_by_timestamp,
    delete_duplicate_test_cases,
    delete_test_cases_by_time_range
)

# Export and statistics utilities
from .utils.export import (
    export_test_cases_to_excel
)

from .utils.stats import (
    get_database_stats,
    count_test_cases_for_test_run
)

# Validation utilities
from .utils.validation import (
    validate_database_connection,
    cleanup_orphaned_test_data
)

# Main function for backward compatibility
def main():
    """
    Main function executed when the module is run directly for testing and initialization.
    
    This function serves as the entry point for standalone execution of the database
    helper package. It performs initial database setup, displays current statistics,
    and can be used for testing database operations during development.
    """
    print("Initializing database...")
    init_db(DATABASE_PATH)
    # Update the database schema to add any missing columns
    update_database_schema(DATABASE_PATH)
    # Update existing test cases with default values
    update_existing_test_cases(DATABASE_PATH)
    print("Database initialization complete.")

# Export all functions for backward compatibility
__all__ = [
    # Core functionality
    'get_thread_local_connection',
    'close_thread_local_connection', 
    'close_connection',
    'DATABASE_PATH',
    'retry_on_db_lock',
    'migrate_database',
    'init_db',
    'backup_database',
    'detect_schema_version',
    'migrate_old_to_new_schema',
    'verify_schema_completeness',
    'standardize_schema',
    'verify_and_update_schema',
    'update_database_schema',
    'update_existing_test_cases',
    
    # Admin operations
    'get_app_config',
    'update_app_config',
    'verify_admin_password',
    'clear_database',
    'clear_database_for_jira',
      # JIRA operations
    'get_or_create_jira_issue',
    'update_jira_issue_in_database',
    'update_jira_issue_enhancement',
    'get_jira_issue_enhancement',
    'delete_jira_issue',
    'delete_all_jira_issues',
    'get_unique_jira_ids',
    'get_enhanced_jira_description',    'save_enhanced_jira_description',    # Test case operations
    'get_highest_test_case_id_number',
    'save_test_cases_to_database',  # Direct function, no wrapper
    'update_test_cases_in_database',
    'delete_test_case',
    'add_single_test_case',
    'add_test_step_to_case',
    'delete_test_step',
      # Test run operations
    'get_latest_test_run',
    'create_test_run',
    'update_test_run',
    'get_test_runs_by_user',
    'get_latest_test_run_id',
    'get_or_create_test_run',
    'update_test_run_completion',
      # Data operations
    'get_test_cases_from_database',
    'get_latest_generated_test_cases',
    'get_all_test_cases_for_user',
    'get_test_cases_by_test_run',
    'get_test_cases_for_test_run',
    'get_latest_generated_test_cases_original',    'fix_all_test_type',
    'get_test_cases_by_filters',
    'get_unified_filtered_test_cases',
    'get_filter_options',
    'get_latest_test_cases',
    'get_most_recent_test_cases_exact',
    'process_backup_style_test_data',
    'count_test_cases_in_database_output',
    '_get_all_test_cases_for_jira_id',
    '_get_test_cases_by_jira_and_type',
    
    # Delete operations
    'delete_test_run',
    'delete_edited_test_cases',
    'delete_all_edited_test_cases',
    'delete_specific_test_cases',
    'delete_by_timestamp',
    'delete_duplicate_test_cases',
    'delete_test_cases_by_time_range',
      # Export and utilities
    'export_test_cases_to_excel',
    'get_database_stats',
    'count_test_cases_for_test_run',
    'validate_database_connection',
    'cleanup_orphaned_test_data',
    
    # Main function
    'main'
]

# Run the main function when this package is executed directly
if __name__ == "__main__":
    main()
