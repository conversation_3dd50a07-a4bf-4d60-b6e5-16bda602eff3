"""
Hybrid Step Editor UI Component for GretahAI ScriptWeaver

This module provides the user interface for the hybrid AI-assisted test case editing system.
It allows users to edit AI-generated steps and add manual steps at various insertion points.

Key Features:
- Full editing capabilities for both AI-generated and manual steps
- Visual distinction between AI and manual steps with origin tracking
- Step insertion interface with templates
- Real-time preview of combined test flow
- Flow validation and warnings
- Integration with StateManager
"""

import streamlit as st
from typing import Dict, List, Any, Optional
from datetime import datetime

from core.step_templates import STEP_TEMPLATES, get_templates_by_category, get_template_categories, create_custom_step_template
from core.step_merger import StepMerger, merge_ai_and_manual_steps
# Import GRETAH standardized logging
from debug_utils import debug


def render_hybrid_step_editor(state) -> bool:
    """
    Render the hybrid step editor interface.

    Args:
        state: StateManager instance

    Returns:
        bool: True if steps were modified, False otherwise
    """
    if not state.hybrid_editing_enabled:
        return False

    steps_modified = False

    st.markdown("### 🔀 Hybrid Step Editor")
    st.markdown("**Edit AI-generated steps** 🤖 and **add manual steps** ✏️ at any insertion point. AI steps maintain their origin tracking for reference.")

    # Initialize hybrid editing data if needed
    if not state.ai_generated_steps:
        # Try to load step data from JSON storage if step_table_json is empty
        if not state.step_table_json:
            try:
                step_table_json = state.get_effective_step_table()
                debug("Loaded step data from JSON storage for hybrid editing",
                      stage="hybrid_editing", operation="data_loading",
                      context={'steps_count': len(step_table_json)})
            except ValueError as e:
                debug(f"Could not load step data for hybrid editing: {e}",
                      stage="hybrid_editing", operation="data_loading_error",
                      context={'error': str(e)})
                # step_table_json remains empty, will show warning below

        # Initialize AI steps if we have step data
        if state.step_table_json:
            state.ai_generated_steps = state.step_table_json.copy()
            debug("Initialized AI-generated steps for hybrid editing",
                  stage="hybrid_editing", operation="initialization",
                  context={'steps_count': len(state.step_table_json)})

    # Create tabs for different views
    tab1, tab2, tab3 = st.tabs(["📝 Edit Steps", "👁️ Preview", "✅ Validate"])

    with tab1:
        steps_modified = _render_step_editing_interface(state)

    with tab2:
        _render_step_preview(state)

    with tab3:
        _render_step_validation(state)

    return steps_modified


def _render_step_editing_interface(state) -> bool:
    """Render the step editing interface."""
    steps_modified = False

    if not state.ai_generated_steps:
        st.warning("⚠️ **No AI-generated steps available for hybrid editing.**")
        st.info("💡 **To use hybrid editing:**\n"
                "1. Make sure you have generated test steps (complete stages 1-3)\n"
                "2. Enable hybrid editing mode using the toggle switch\n"
                "3. If you have existing steps, try refreshing the page")

        # Show debug information if step data exists but AI steps are not initialized
        if state.step_table_json:
            st.error(f"🐛 **Debug Info:** Found {len(state.step_table_json)} steps in step_table_json but ai_generated_steps is empty. This indicates an initialization issue.")
        elif hasattr(state, 'selected_test_case') and state.selected_test_case:
            test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown')
            st.info(f"📋 **Current test case:** {test_case_id}")
            st.info("🔍 **Troubleshooting:** Try going back to Stage 3 and regenerating the step table.")

        return False

    # Step insertion controls
    st.markdown("#### ➕ Add Manual Steps")

    col1, col2 = st.columns([1, 1])

    with col1:
        # Insertion point selection
        merger = StepMerger()
        merger.set_ai_steps(state.ai_generated_steps)
        insertion_points = merger.get_insertion_points()

        selected_point = st.selectbox(
            "Insert at:",
            insertion_points,
            key="insertion_point_select",
            help="Choose where to insert the new step"
        )

    with col2:
        # Template category selection
        categories = get_template_categories()
        selected_category = st.selectbox(
            "Step Category:",
            ["Custom"] + categories,
            key="template_category_select",
            help="Choose a category of predefined steps"
        )

    # Template selection or custom step creation
    if selected_category == "Custom":
        if _render_custom_step_creator(state, selected_point):
            steps_modified = True
    else:
        if _render_template_step_creator(state, selected_point, selected_category):
            steps_modified = True

    # Render the unified step interface for editing existing steps
    st.markdown("---")  # Visual separator
    st.markdown("#### ✏️ Edit Existing Steps")
    if _render_unified_step_interface(state):
        steps_modified = True

    return steps_modified


def _render_custom_step_creator(state, insertion_point: str) -> bool:
    """Render interface for creating custom steps or editing AI steps."""
    # Check if this is an AI step being edited
    is_editing_ai_step = _is_ai_step_number(insertion_point, state)
    ai_step_data = None

    if is_editing_ai_step:
        ai_step_data = _get_ai_step_data_for_editing(state, insertion_point)
        st.markdown(f"**🤖➡️✏️ Convert AI Step {insertion_point} to Manual Step**")
        st.info(f"You are converting AI-generated step {insertion_point} into a fully editable manual step. The original AI step will be replaced.")
    else:
        st.markdown("**Create Custom Step**")

    with st.form(key="custom_step_form"):
        col1, col2 = st.columns(2)

        # Pre-populate form fields if editing an AI step
        default_action = ai_step_data.get("action", "") if ai_step_data else ""
        default_locator_strategy = ai_step_data.get("locator_strategy", "") if ai_step_data else ""
        default_locator = ai_step_data.get("locator", "") if ai_step_data else ""
        default_test_data = ai_step_data.get("test_data_param", "") if ai_step_data else ""
        default_expected_result = ai_step_data.get("expected_result", "") if ai_step_data else ""
        default_timeout = ai_step_data.get("timeout", 10) if ai_step_data else 10

        with col1:
            action = st.text_input("Action", value=default_action, placeholder="e.g., click, type, verify")

            # Set the correct index for locator strategy selectbox
            locator_strategies = ["", "css", "xpath", "id", "name", "aria"]
            default_strategy_index = 0
            if default_locator_strategy in locator_strategies:
                default_strategy_index = locator_strategies.index(default_locator_strategy)

            locator_strategy = st.selectbox("Locator Strategy", locator_strategies, index=default_strategy_index)
            locator = st.text_input("Locator", value=default_locator, placeholder="e.g., #button-id, //input[@name='username']")

        with col2:
            test_data_param = st.text_input("Test Data", value=default_test_data, placeholder="e.g., {{username}}, <EMAIL>")
            expected_result = st.text_input("Expected Result", value=default_expected_result, placeholder="e.g., Button clicked successfully")
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=300, value=int(default_timeout))

        # Dynamic button text based on operation type
        button_text = f"🔄 Convert AI Step {insertion_point}" if is_editing_ai_step else "➕ Add Custom Step"
        submitted = st.form_submit_button(button_text)

        if submitted and action:
            if is_editing_ai_step:
                # Handle AI step conversion
                # Generate step number for the converted step
                next_step_no = insertion_point  # Keep the same step number

                # Create manual step from AI step data
                converted_step = create_custom_step_template(
                    step_no=next_step_no,
                    action=action,
                    locator_strategy=locator_strategy,
                    locator=locator,
                    test_data_param=test_data_param,
                    expected_result=expected_result,
                    timeout=timeout
                )

                # Convert AI step to manual step
                if _convert_ai_step_to_manual(state, insertion_point, converted_step):
                    st.success(f"✅ Converted AI step {insertion_point} to manual step: {action}")
                    st.rerun()
                    return True
                else:
                    st.error(f"❌ Failed to convert AI step {insertion_point}")
                    return False
            else:
                # Handle regular manual step creation
                # Generate next step number
                next_step_no = _get_next_step_number(state, insertion_point)

                # Create custom step
                custom_step = create_custom_step_template(
                    step_no=next_step_no,
                    action=action,
                    locator_strategy=locator_strategy,
                    locator=locator,
                    test_data_param=test_data_param,
                    expected_result=expected_result,
                    timeout=timeout
                )

                # Add to state
                _add_manual_step_to_state(state, custom_step, insertion_point)
                st.success(f"✅ Added custom step: {action}")
                st.rerun()
                return True

    return False


def _render_template_step_creator(state, insertion_point: str, category: str) -> bool:
    """Render interface for creating steps from templates."""
    templates = get_templates_by_category(category)

    if not templates:
        st.info(f"No templates available for category: {category}")
        return False

    st.markdown(f"**{category.title()} Templates**")

    # Template selection
    template_options = {f"{template.name} - {template.description}": template_id
                       for template_id, template in templates.items()}

    selected_template_display = st.selectbox(
        "Choose Template:",
        list(template_options.keys()),
        key=f"template_select_{category}"
    )

    if selected_template_display:
        template_id = template_options[selected_template_display]
        template = templates[template_id]

        # Show template details
        with st.expander("📋 Template Details", expanded=False):
            st.markdown(f"**Description:** {template.description}")
            st.markdown(f"**Category:** {template.category}")
            if template.customizable_fields:
                st.markdown(f"**Customizable Fields:** {', '.join(template.customizable_fields)}")

        # Customization form
        with st.form(key=f"template_form_{template_id}"):
            customizations = {}

            if template.customizable_fields:
                st.markdown("**Customize Template:**")
                col1, col2 = st.columns(2)

                for i, field in enumerate(template.customizable_fields):
                    current_value = template.step_data.get(field, "")

                    with col1 if i % 2 == 0 else col2:
                        if field == "timeout":
                            customizations[field] = st.number_input(
                                f"{field.replace('_', ' ').title()}:",
                                min_value=1, max_value=300,
                                value=int(current_value) if current_value else 10,
                                key=f"custom_{field}_{template_id}"
                            )
                        else:
                            customizations[field] = st.text_input(
                                f"{field.replace('_', ' ').title()}:",
                                value=str(current_value),
                                key=f"custom_{field}_{template_id}"
                            )

            submitted = st.form_submit_button(f"➕ Add {template.name}")

            if submitted:
                # Generate next step number
                next_step_no = _get_next_step_number(state, insertion_point)

                # Create step from template
                step = template.create_step(next_step_no, customizations)

                # Add to state
                _add_manual_step_to_state(state, step, insertion_point)
                st.success(f"✅ Added step: {template.name}")
                st.rerun()
                return True

    return False


def _render_unified_step_interface(state) -> bool:
    """Render unified interface for both AI-generated and manual steps."""
    if not state.ai_generated_steps and not state.step_insertion_points:
        st.info("No steps available. Generate AI steps or add manual steps to get started.")
        return False

    steps_modified = False

    # Create a unified step structure organized by flow position
    unified_steps = _organize_steps_by_flow_position(state)

    # Check if any step is currently being edited (to render external buttons)
    currently_editing_step = None
    for section_key, section_data in unified_steps.items():
        for step_data in section_data['steps']:
            editing_key = f"editing_{step_data['key_prefix']}"
            if st.session_state.get(editing_key, False):
                currently_editing_step = step_data
                break
        if currently_editing_step:
            break

    # Render each section
    for section_key, section_data in unified_steps.items():
        section_title = section_data['title']
        section_steps = section_data['steps']
        section_expanded = section_data.get('expanded', False)

        if section_steps:  # Only show sections that have steps
            with st.expander(f"{section_title} ({len(section_steps)} steps)", expanded=section_expanded):
                for step_data in section_steps:
                    if _render_unified_step_card(state, step_data, skip_edit_form=True):
                        steps_modified = True

    # Render external edit form and buttons outside all expanders
    if currently_editing_step:
        st.markdown("---")  # Visual separator
        st.markdown("### ✏️ Editing Step")
        if _render_external_edit_form(state, currently_editing_step):
            steps_modified = True

    return steps_modified


def _organize_steps_by_flow_position(state) -> dict:
    """Organize all steps (AI + manual) by their position in the test flow."""
    unified_steps = {}

    # Add AI steps as the main flow
    if state.ai_generated_steps:
        ai_steps_data = []
        for i, step in enumerate(state.ai_generated_steps):
            ai_steps_data.append({
                'type': 'ai',
                'step': step,
                'index': i,
                'key_prefix': f'ai_{i}'
            })

        unified_steps['main_flow'] = {
            'title': 'AI Steps',
            'steps': ai_steps_data,
            'expanded': True
        }

    # Add manual steps organized by insertion points
    if state.step_insertion_points:
        for insertion_point, steps in state.step_insertion_points.items():
            if steps:
                manual_steps_data = []
                for i, step in enumerate(steps):
                    # Determine step type based on conversion status
                    if step.get('_converted_from_ai', False):
                        step_type = 'converted'
                    else:
                        step_type = 'manual'

                    manual_steps_data.append({
                        'type': step_type,
                        'step': step,
                        'index': i,
                        'insertion_point': insertion_point,
                        'key_prefix': f'manual_{insertion_point}_{i}'
                    })

                # Create readable title for insertion point with special handling for converted steps
                if insertion_point.startswith('converted_'):
                    original_step_no = insertion_point.replace('converted_', '')

                    # Get category from the first step if available
                    category_info = ""
                    if manual_steps_data and manual_steps_data[0]['step'].get('_template_category'):
                        category = manual_steps_data[0]['step']['_template_category']
                        category_display = category.replace('_', ' ').title()
                        category_info = f" ({category_display})"

                    readable_title = f"🤖➡️✏️ Converted from AI Step {original_step_no}{category_info}"
                    section_key = f'converted_{insertion_point}'
                    expanded = True  # Show converted steps expanded by default
                else:
                    readable_title = insertion_point.replace('_', ' ').title()
                    section_key = f'manual_{insertion_point}'
                    expanded = False

                unified_steps[section_key] = {
                    'title': f'✏️ Manual Steps - {readable_title}',
                    'steps': manual_steps_data,
                    'expanded': expanded
                }

    return unified_steps


def _render_unified_step_card(state, step_data: dict, skip_edit_form: bool = False) -> bool:
    """Render a unified step card that works for both AI and manual steps."""
    step = step_data['step']
    step_type = step_data['type']
    step_index = step_data['index']
    key_prefix = step_data['key_prefix']

    # Use Streamlit's native container approach - clean and simple
    with st.container():
        # Create three-column layout (consistent with manual steps pattern)
        col1, col2, col3 = st.columns([3, 1, 1])

        # Column 1: Step information
        with col1:
            _render_step_info_column(step, step_type)

        # Column 2: Edit button
        with col2:
            _render_edit_button_column(step_data, key_prefix)

        # Column 3: Action button (delete for manual, reset for modified AI)
        with col3:
            _render_action_button_column(state, step_data, key_prefix)

        # Add a small visual spacer between step cards
        st.markdown("")  # Empty line for spacing

    # Edit form (if editing and not skipped)
    editing_key = f"editing_{key_prefix}"
    if not skip_edit_form and st.session_state.get(editing_key, False):
        return _render_unified_edit_form(state, step_data, editing_key)

    return False


def _render_step_info_column(step: dict, step_type: str):
    """Render the step information column with clean, minimal styling."""
    # Create a clean header with step information
    step_no = step.get('step_no', 'N/A')
    action = step.get('action', 'Unknown')
    expected_result = step.get('expected_result', 'N/A')

    # Determine step type badge
    if step_type == 'ai':
        if step.get('_ai_step_modified', False):
            type_badge = '<span style="background-color: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: 500;">🤖 AI Modified</span>'
        else:
            type_badge = '<span style="background-color: #f8f9fa; color: #6c757d; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: 500;">🤖 AI Generated</span>'
    elif step_type == 'converted':
        original_step_no = step.get('_original_ai_step_no', 'N/A')
        type_badge = f'<span style="background-color: #e7f3ff; color: #0c5460; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: 500;">🤖➡️✏️ Converted from AI {original_step_no}</span>'
    else:
        type_badge = '<span style="background-color: #d4edda; color: #155724; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: 500;">✏️ Manual</span>'

    # Render step information with clean layout
    st.markdown(f"**Step {step_no}:** {action}")
    st.markdown(f"*Expected:* {expected_result}")
    st.markdown(type_badge, unsafe_allow_html=True)

    # Add step type info if available
    if step.get('step_type'):
        st.caption(f"Type: {step.get('step_type', 'ui').title()}")


    # Show additional metadata for specific step types
    if step_type == 'converted' and step.get('_conversion_timestamp'):
        from datetime import datetime
        try:
            conversion_time = datetime.fromisoformat(step['_conversion_timestamp'])
            st.caption(f"Converted: {conversion_time.strftime('%Y-%m-%d %H:%M')}")
        except:
            pass

    # Show category for manual and converted steps
    if step_type in ['manual', 'converted'] and step.get('_template_category'):
        category_display = step.get('_template_category', '').replace('_', ' ').title()
        st.caption(f"Category: {category_display}")

    # Show insertion point for manual steps
    if step_type == 'manual' and step.get('_insertion_point'):
        insertion_readable = step.get('_insertion_point', '').replace('_', ' ').title()
        st.caption(f"Position: {insertion_readable}")


def _render_edit_button_column(step_data: dict, key_prefix: str):
    """Render the edit button column with consistent styling."""
    editing_key = f"editing_{key_prefix}"
    step_type = step_data['type']

    # Determine button help text based on step type
    if step_type == 'ai':
        button_help = "Edit AI step"
    elif step_type == 'converted':
        button_help = "Edit converted step (originally AI-generated)"
    else:
        button_help = "Edit manual step"

    if st.button("✏️ Edit", key=f"edit_{key_prefix}", help=button_help, use_container_width=True):
        st.session_state[editing_key] = True
        st.rerun()


def _render_action_button_column(state, step_data: dict, key_prefix: str):
    """Render the action button column (delete/reset) with consistent styling."""
    step = step_data['step']
    step_type = step_data['type']

    if step_type == 'manual':
        # Delete button for manual steps
        if st.button("🗑️ Delete", key=f"delete_{key_prefix}", help="Delete manual step",
                    use_container_width=True, type="secondary"):
            insertion_point = step_data['insertion_point']
            step_index = step_data['index']
            state.step_insertion_points[insertion_point].pop(step_index)
            st.success("Manual step deleted")
            st.rerun()

    elif step_type == 'ai':
        # Reset button for modified AI steps, info for unmodified
        if step.get('_ai_step_modified', False):
            confirm_key = f"confirm_reset_{key_prefix}"
            if st.session_state.get(confirm_key, False):
                # Show confirmation buttons
                col_a, col_b = st.columns(2)
                with col_a:
                    if st.button("✅", key=f"confirm_yes_{key_prefix}", help="Confirm reset"):
                        step_index = step_data['index']
                        if state.reset_ai_step_to_original(step_index):
                            st.success("Step reset to original AI version")
                            # Clear confirmation state
                            if confirm_key in st.session_state:
                                del st.session_state[confirm_key]
                            st.rerun()
                        else:
                            st.error("Failed to reset step")
                with col_b:
                    if st.button("❌", key=f"confirm_no_{key_prefix}", help="Cancel reset"):
                        del st.session_state[confirm_key]
                        st.rerun()
            else:
                if st.button("🔄 Reset", key=f"reset_{key_prefix}", help="Reset to original AI version",
                           use_container_width=True, type="secondary"):
                    st.session_state[confirm_key] = True
                    st.rerun()
        else:
            # Show subtle AI badge for unmodified steps
            st.markdown(
                '<div style="text-align: center; padding: 4px 8px; background-color: #f8f9fa; '
                'border: 1px solid #e9ecef; border-radius: 4px; font-size: 11px; color: #6c757d; font-weight: 500;">🤖 AI Generated</div>',
                unsafe_allow_html=True
            )


# These functions have been replaced by the unified interface implementation
# _render_ai_steps_with_edit() and _render_existing_manual_steps() are no longer used


def _render_unified_edit_form(state, step_data: dict, editing_key: str) -> bool:
    """Render unified edit form that handles both AI and manual steps."""
    step = step_data['step']
    step_type = step_data['type']
    key_prefix = step_data['key_prefix']

    # Determine form title and styling based on step type
    if step_type == 'ai':
        form_title = "🤖 Edit AI-Generated Step"
        form_subtitle = "Editing will mark this step as modified while preserving its AI origin tracking."
        form_key = f"edit_ai_form_{key_prefix}"
    elif step_type == 'converted':
        form_title = "🤖➡️✏️ Edit Converted Step"
        original_step_no = step.get('_original_ai_step_no', 'N/A')
        form_subtitle = f"This step was converted from AI Step {original_step_no} and is now fully editable as a manual step."
        form_key = f"edit_converted_form_{key_prefix}"
    else:
        form_title = "✏️ Edit Manual Step"
        form_subtitle = "Modify the manual step details as needed."
        form_key = f"edit_manual_form_{key_prefix}"

    # Initialize session state for form data if not exists
    form_data_key = f"form_data_{key_prefix}"
    if form_data_key not in st.session_state:
        st.session_state[form_data_key] = {
            'action': step.get("action", ""),
            'locator_strategy': step.get("locator_strategy", ""),
            'locator': step.get("locator", ""),
            'test_data_param': step.get("test_data_param", ""),
            'expected_result': step.get("expected_result", ""),
            'timeout': step.get("timeout", 10),
            'step_type': step.get("step_type", "ui"),
            'assertion_type': step.get("assertion_type", "no_error"),
            'step_description': step.get("step_description", ""),
            'condition': step.get("condition", "")
        }

    # Render form inside an expander for better organization
    with st.expander(f"✏️ {form_title}", expanded=True):
        st.caption(form_subtitle)

        # Use consistent form layout for both step types
        col1, col2 = st.columns(2)

        with col1:
            # Update session state when form fields change
            action = st.text_input("Action",
                                 value=st.session_state[form_data_key]['action'],
                                 key=f"action_{key_prefix}",
                                 help="Describe what this step does",
                                 on_change=lambda: st.session_state[form_data_key].update({'action': st.session_state[f"action_{key_prefix}"]}))

            locator_strategy_options = ["", "css", "xpath", "id", "name", "aria", "url"]
            current_strategy = st.session_state[form_data_key]['locator_strategy']
            strategy_index = locator_strategy_options.index(current_strategy) if current_strategy in locator_strategy_options else 0

            locator_strategy = st.selectbox("Locator Strategy", locator_strategy_options,
                                          index=strategy_index,
                                          key=f"locator_strategy_{key_prefix}",
                                          help="How to locate the element",
                                          on_change=lambda: st.session_state[form_data_key].update({'locator_strategy': st.session_state[f"locator_strategy_{key_prefix}"]}))
            locator = st.text_input("Locator",
                                  value=st.session_state[form_data_key]['locator'],
                                  key=f"locator_{key_prefix}",
                                  help="The locator value (CSS selector, XPath, etc.)",
                                  on_change=lambda: st.session_state[form_data_key].update({'locator': st.session_state[f"locator_{key_prefix}"]}))

        with col2:
            test_data_param = st.text_input("Test Data",
                                          value=st.session_state[form_data_key]['test_data_param'],
                                          key=f"test_data_{key_prefix}",
                                          help="Test data parameter (e.g., {{username}})",
                                          on_change=lambda: st.session_state[form_data_key].update({'test_data_param': st.session_state[f"test_data_{key_prefix}"]}))

            expected_result = st.text_input("Expected Result",
                                          value=st.session_state[form_data_key]['expected_result'],
                                          key=f"expected_result_{key_prefix}",
                                          help="What should happen when this step executes",
                                          on_change=lambda: st.session_state[form_data_key].update({'expected_result': st.session_state[f"expected_result_{key_prefix}"]}))

            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=300,
                                    value=int(st.session_state[form_data_key]['timeout']),
                                    key=f"timeout_{key_prefix}",
                                    help="Maximum time to wait for this step",
                                    on_change=lambda: st.session_state[form_data_key].update({'timeout': st.session_state[f"timeout_{key_prefix}"]}))

        # Additional fields for AI steps (more comprehensive editing)
        if step_type == 'ai':
            step_type_options = ["ui", "setup", "teardown", "assertion", "navigation"]
            current_step_type = st.session_state[form_data_key]['step_type']
            step_type_index = step_type_options.index(current_step_type) if current_step_type in step_type_options else 0

            step_type_field = st.selectbox("Step Type", step_type_options,
                                         index=step_type_index,
                                         key=f"step_type_{key_prefix}",
                                         help="Type of step for categorization",
                                         on_change=lambda: st.session_state[form_data_key].update({'step_type': st.session_state[f"step_type_{key_prefix}"]}))

            assertion_type_options = ["", "text_equals", "text_contains", "url_contains", "element_visible", "no_error"]
            current_assertion = st.session_state[form_data_key]['assertion_type']
            assertion_index = assertion_type_options.index(current_assertion) if current_assertion in assertion_type_options else 0

            assertion_type = st.selectbox("Assertion Type", assertion_type_options,
                                        index=assertion_index,
                                        key=f"assertion_type_{key_prefix}",
                                        help="Type of assertion to perform",
                                        on_change=lambda: st.session_state[form_data_key].update({'assertion_type': st.session_state[f"assertion_type_{key_prefix}"]}))

            step_description = st.text_area("Step Description",
                                           value=st.session_state[form_data_key]['step_description'],
                                           key=f"step_description_{key_prefix}",
                                           help="Human-readable description of what this step does",
                                           on_change=lambda: st.session_state[form_data_key].update({'step_description': st.session_state[f"step_description_{key_prefix}"]}))

            condition = st.text_input("Condition",
                                    value=st.session_state[form_data_key]['condition'],
                                    key=f"condition_{key_prefix}",
                                    help="Optional condition for step execution",
                                    on_change=lambda: st.session_state[form_data_key].update({'condition': st.session_state[f"condition_{key_prefix}"]}))

    # External buttons outside the expander - always visible
    st.markdown("---")  # Visual separator

    # Create button columns
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.markdown("**Actions:**")

    with col2:
        # Apply Changes button - always visible and prominent
        if st.button("✅ Apply Changes",
                    key=f"apply_changes_{key_prefix}",
                    help="Save all modifications to this step",
                    type="primary",
                    use_container_width=True):
            return _handle_external_form_submission(state, step_data, editing_key, form_data_key, step_type)

    with col3:
        # Cancel button
        if st.button("❌ Cancel",
                    key=f"cancel_edit_{key_prefix}",
                    help="Discard changes and close editor",
                    use_container_width=True):
            # Clean up session state
            if form_data_key in st.session_state:
                del st.session_state[form_data_key]
            if editing_key in st.session_state:
                del st.session_state[editing_key]
            st.rerun()
            return False

    return False


def _render_external_edit_form(state, step_data: dict) -> bool:
    """Render the edit form outside of any expanders for true external visibility."""
    step = step_data['step']
    step_type = step_data['type']
    key_prefix = step_data['key_prefix']
    editing_key = f"editing_{key_prefix}"

    # Determine form title and styling based on step type
    if step_type == 'ai':
        form_title = "🤖 Edit AI-Generated Step"
        form_subtitle = "Editing will mark this step as modified while preserving its AI origin tracking."
    elif step_type == 'converted':
        form_title = "🤖➡️✏️ Edit Converted Step"
        original_step_no = step.get('_original_ai_step_no', 'N/A')
        form_subtitle = f"This step was converted from AI Step {original_step_no} and is now fully editable as a manual step."
    else:
        form_title = "✏️ Edit Manual Step"
        form_subtitle = "This is a manually created step that can be freely modified."

    st.markdown(f"#### {form_title}")
    st.markdown(form_subtitle)

    # Initialize form data in session state if not exists
    form_data_key = f"form_data_{key_prefix}"
    if form_data_key not in st.session_state:
        st.session_state[form_data_key] = {
            'action': step.get('action', ''),
            'locator_strategy': step.get('locator_strategy', ''),
            'locator': step.get('locator', ''),
            'test_data_param': step.get('test_data_param', ''),
            'expected_result': step.get('expected_result', ''),
            'timeout': step.get('timeout', 30),
            'step_description': step.get('step_description', ''),
            'condition': step.get('condition', ''),
            'assertion_type': step.get('assertion_type', 'no_error'),
            'step_type': step.get('step_type', 'ui')
        }

    # Add enhanced editing options for AI steps
    if step_type == 'ai':
        # Visual indicator for enhanced mode
        st.markdown(
            '<div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; padding: 8px; margin: 8px 0;">'
            '<strong>🎯 Enhanced Editing Mode</strong> - Full feature set with templates and categories'
            '</div>',
            unsafe_allow_html=True
        )

        # Category and template selection
        col_cat, col_template = st.columns(2)

        with col_cat:
            # Get current category or auto-detect from action
            current_action = st.session_state[form_data_key]['action']
            from core.step_templates import categorize_action, get_template_categories
            auto_category = categorize_action(current_action)

            categories = get_template_categories()
            category_options = ["Keep Current"] + categories

            selected_category = st.selectbox(
                "Step Category:",
                category_options,
                key=f"category_select_{key_prefix}",
                help="Choose a category to access predefined templates and suggestions"
            )

        with col_template:
            # Template selection based on category
            if selected_category != "Keep Current":
                from core.step_templates import get_templates_by_category
                templates = get_templates_by_category(selected_category)

                if templates:
                    template_options = ["Custom"] + [f"{template.name} - {template.description}"
                                                   for template in templates.values()]

                    selected_template = st.selectbox(
                        "Apply Template:",
                        template_options,
                        key=f"template_select_{key_prefix}",
                        help="Apply a predefined template to this step"
                    )

                    # Apply template if selected
                    if selected_template != "Custom" and st.button("📋 Apply Template", key=f"apply_template_{key_prefix}"):
                        template_name = selected_template.split(" - ")[0]
                        for template in templates.values():
                            if template.name == template_name:
                                # Update form data with template values
                                template_data = template.step_data.copy()

                                # Update the main form data
                                st.session_state[form_data_key].update({
                                    'action': template_data.get('action', st.session_state[form_data_key]['action']),
                                    'locator_strategy': template_data.get('locator_strategy', st.session_state[form_data_key]['locator_strategy']),
                                    'locator': template_data.get('locator', st.session_state[form_data_key]['locator']),
                                    'test_data_param': template_data.get('test_data_param', st.session_state[form_data_key]['test_data_param']),
                                    'expected_result': template_data.get('expected_result', st.session_state[form_data_key]['expected_result']),
                                    'assertion_type': template_data.get('assertion_type', st.session_state[form_data_key]['assertion_type']),
                                    'timeout': template_data.get('timeout', st.session_state[form_data_key]['timeout']),
                                    'step_type': template_data.get('step_type', st.session_state[form_data_key]['step_type']),
                                    'condition': template_data.get('condition', st.session_state[form_data_key]['condition'])
                                })

                                # Also update the individual form field keys to ensure immediate visual update
                                st.session_state[f"action_{key_prefix}_ext"] = template_data.get('action', st.session_state[form_data_key]['action'])
                                st.session_state[f"locator_strategy_{key_prefix}_ext"] = template_data.get('locator_strategy', st.session_state[form_data_key]['locator_strategy'])
                                st.session_state[f"locator_{key_prefix}_ext"] = template_data.get('locator', st.session_state[form_data_key]['locator'])
                                st.session_state[f"test_data_param_{key_prefix}_ext"] = template_data.get('test_data_param', st.session_state[form_data_key]['test_data_param'])
                                st.session_state[f"expected_result_{key_prefix}_ext"] = template_data.get('expected_result', st.session_state[form_data_key]['expected_result'])
                                st.session_state[f"assertion_type_{key_prefix}_ext"] = template_data.get('assertion_type', st.session_state[form_data_key]['assertion_type'])
                                st.session_state[f"timeout_{key_prefix}_ext"] = template_data.get('timeout', st.session_state[form_data_key]['timeout'])
                                st.session_state[f"step_type_{key_prefix}_ext"] = template_data.get('step_type', st.session_state[form_data_key]['step_type'])
                                st.session_state[f"condition_{key_prefix}_ext"] = template_data.get('condition', st.session_state[form_data_key]['condition'])

                                st.success(f"✅ Applied template: {template.name}")
                                st.rerun()
                else:
                    st.info("No templates available for this category")
            else:
                st.info("Select a category to see available templates")

        st.markdown("---")

    # Render form fields in a container (not a form to avoid submission issues)
    with st.container():
        col1, col2 = st.columns(2)

        with col1:
            action = st.text_input("Action",
                                 value=st.session_state[form_data_key]['action'],
                                 key=f"action_{key_prefix}_ext",
                                 help="The action to perform (e.g., click, type, verify)",
                                 on_change=lambda: st.session_state[form_data_key].update({'action': st.session_state[f"action_{key_prefix}_ext"]}))

            locator_strategy = st.selectbox("Locator Strategy",
                                          ["xpath", "css", "id", "name", "class", "tag", "link_text", "partial_link_text"],
                                          index=["xpath", "css", "id", "name", "class", "tag", "link_text", "partial_link_text"].index(st.session_state[form_data_key]['locator_strategy']) if st.session_state[form_data_key]['locator_strategy'] in ["xpath", "css", "id", "name", "class", "tag", "link_text", "partial_link_text"] else 0,
                                          key=f"locator_strategy_{key_prefix}_ext",
                                          help="Strategy to locate the element",
                                          on_change=lambda: st.session_state[form_data_key].update({'locator_strategy': st.session_state[f"locator_strategy_{key_prefix}_ext"]}))

            locator = st.text_input("Locator",
                                  value=st.session_state[form_data_key]['locator'],
                                  key=f"locator_{key_prefix}_ext",
                                  help="Element locator (XPath, CSS selector, etc.)",
                                  on_change=lambda: st.session_state[form_data_key].update({'locator': st.session_state[f"locator_{key_prefix}_ext"]}))

            test_data_param = st.text_input("Test Data Parameter",
                                          value=st.session_state[form_data_key]['test_data_param'],
                                          key=f"test_data_param_{key_prefix}_ext",
                                          help="Parameter name for test data (optional)",
                                          on_change=lambda: st.session_state[form_data_key].update({'test_data_param': st.session_state[f"test_data_param_{key_prefix}_ext"]}))

            # Add step type selection
            step_type_options = ["ui", "api", "assertion", "setup", "teardown", "wait"]
            step_type_index = step_type_options.index(st.session_state[form_data_key]['step_type']) if st.session_state[form_data_key]['step_type'] in step_type_options else 0

            step_type_sel = st.selectbox("Step Type",
                                       step_type_options,
                                       index=step_type_index,
                                       key=f"step_type_{key_prefix}_ext",
                                       help="Type of step operation",
                                       on_change=lambda: st.session_state[form_data_key].update({'step_type': st.session_state[f"step_type_{key_prefix}_ext"]}))

        with col2:
            expected_result = st.text_area("Expected Result",
                                         value=st.session_state[form_data_key]['expected_result'],
                                         key=f"expected_result_{key_prefix}_ext",
                                         help="What should happen when this step is executed",
                                         height=100,
                                         on_change=lambda: st.session_state[form_data_key].update({'expected_result': st.session_state[f"expected_result_{key_prefix}_ext"]}))

            timeout = st.number_input("Timeout (seconds)",
                                    min_value=1,
                                    max_value=300,
                                    value=st.session_state[form_data_key]['timeout'],
                                    key=f"timeout_{key_prefix}_ext",
                                    help="Maximum time to wait for this step",
                                    on_change=lambda: st.session_state[form_data_key].update({'timeout': st.session_state[f"timeout_{key_prefix}_ext"]}))

            # Add assertion type selection
            assertion_options = ["no_error", "element_present", "element_not_present", "text_present", "text_not_present",
                                "element_value", "element_attribute", "page_title", "url_contains", "custom"]
            assertion_index = assertion_options.index(st.session_state[form_data_key]['assertion_type']) if st.session_state[form_data_key]['assertion_type'] in assertion_options else 0

            assertion_type = st.selectbox("Assertion Type",
                                        assertion_options,
                                        index=assertion_index,
                                        key=f"assertion_type_{key_prefix}_ext",
                                        help="Type of assertion to perform",
                                        on_change=lambda: st.session_state[form_data_key].update({'assertion_type': st.session_state[f"assertion_type_{key_prefix}_ext"]}))

            step_description = st.text_area("Step Description",
                                           value=st.session_state[form_data_key]['step_description'],
                                           key=f"step_description_{key_prefix}_ext",
                                           help="Human-readable description of what this step does",
                                           height=80,
                                           on_change=lambda: st.session_state[form_data_key].update({'step_description': st.session_state[f"step_description_{key_prefix}_ext"]}))

            condition = st.text_input("Condition",
                                    value=st.session_state[form_data_key]['condition'],
                                    key=f"condition_{key_prefix}_ext",
                                    help="Optional condition for step execution",
                                    on_change=lambda: st.session_state[form_data_key].update({'condition': st.session_state[f"condition_{key_prefix}_ext"]}))

    # External buttons - truly outside any expander
    st.markdown("---")  # Visual separator

    # Create button columns
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.markdown("**Actions:**")

    with col2:
        # Apply Changes button - always visible and prominent
        if st.button("✅ Apply Changes",
                    key=f"apply_changes_ext_{key_prefix}",
                    help="Save all modifications to this step",
                    type="primary",
                    use_container_width=True):
            return _handle_external_form_submission(state, step_data, editing_key, form_data_key, step_type)

    with col3:
        # Cancel button
        if st.button("❌ Cancel",
                    key=f"cancel_edit_ext_{key_prefix}",
                    help="Discard changes and close editor",
                    use_container_width=True):
            # Clean up session state
            if form_data_key in st.session_state:
                del st.session_state[form_data_key]
            if editing_key in st.session_state:
                del st.session_state[editing_key]
            st.rerun()
            return False

    return False


def _handle_external_form_submission(state, step_data: dict, editing_key: str, form_data_key: str, step_type: str) -> bool:
    """Handle form submission from external Apply Changes button."""
    # Get form data from session state
    if form_data_key not in st.session_state:
        st.error("❌ Form data not found. Please try again.")
        return False

    form_data = st.session_state[form_data_key].copy()

    # Call the existing unified form submission handler
    result = _handle_unified_form_submission(state, step_data, editing_key, form_data)

    # Clean up session state if successful
    if result:
        if form_data_key in st.session_state:
            del st.session_state[form_data_key]

    return result


def _handle_unified_form_submission(state, step_data: dict, editing_key: str, form_data: dict) -> bool:
    """Handle form submission for both AI and manual steps."""
    step = step_data['step']
    step_type = step_data['type']

    # Validate the changes
    validation_result = _validate_ai_step_modification(form_data, step)

    if not validation_result["is_valid"]:
        # Show validation errors
        for error in validation_result["errors"]:
            st.error(f"❌ {error}")
        return False

    # Debug: Log step modification with object identity
    step_id = id(step)
    debug(f"Modifying step: {step.get('step_no', 'N/A')} - {step.get('action', 'Unknown')} (object id: {step_id})",
          stage="hybrid_editing", operation="step_modification",
          context={'step_type': step_type, 'step_no': step.get('step_no'),
                  'original_action': step.get('action'), 'new_action': form_data.get('action'),
                  'step_object_id': step_id})

    # Debug: Verify this step is in ai_generated_steps
    if step_type == 'ai' and hasattr(state, 'ai_generated_steps') and state.ai_generated_steps:
        step_found = False
        for i, ai_step in enumerate(state.ai_generated_steps):
            if id(ai_step) == step_id:
                step_found = True
                debug(f"Found step object in ai_generated_steps at index {i}",
                      stage="hybrid_editing", operation="step_reference_verification",
                      context={'step_index': i, 'step_no': ai_step.get('step_no')})
                break
        if not step_found:
            debug("WARNING: Step object not found in ai_generated_steps - this may cause sync issues",
                  stage="hybrid_editing", operation="step_reference_warning",
                  context={'step_object_id': step_id, 'step_no': step.get('step_no')})

    # Update step with form data
    step.update(form_data)

    # Add type-specific metadata
    if step_type == 'ai':
        step.update({
            "_ai_step_modified": True,
            "_modified_at": datetime.now().isoformat(),
            "_modified_by": "user_edit"
        })
        success_message = "🤖 AI step updated successfully"

        # Debug: Verify the flag was set
        debug(f"Set _ai_step_modified=True for step {step.get('step_no', 'N/A')}",
              stage="hybrid_editing", operation="flag_verification",
              context={'step_no': step.get('step_no'), 'flag_value': step.get('_ai_step_modified'),
                      'step_object_id': id(step)})

        # Debug: Verify AI step is in the state
        if hasattr(state, 'ai_generated_steps') and state.ai_generated_steps:
            ai_step_count = len(state.ai_generated_steps)
            modified_count = sum(1 for s in state.ai_generated_steps if s.get('_ai_step_modified', False))
            debug(f"AI steps after modification: {ai_step_count} total, {modified_count} modified",
                  stage="hybrid_editing", operation="step_count_verification",
                  context={'ai_step_count': ai_step_count, 'modified_count': modified_count})
    else:
        step.update({
            "_modified_at": datetime.now().isoformat()
        })
        success_message = "✏️ Manual step updated successfully"

    # Clear editing state
    del st.session_state[editing_key]

    # Synchronize state after AI step modification
    if step_type == 'ai' and hasattr(state, 'sync_step_table_with_combined'):
        debug("Synchronizing state after AI step modification",
              stage="hybrid_editing", operation="state_synchronization")
        state.sync_step_table_with_combined()

        # Save the updated step data to persistent storage
        if hasattr(state, 'save_step_data_to_json') and hasattr(state, 'step_table_json'):
            debug("Saving modified AI steps to persistent storage",
                  stage="hybrid_editing", operation="persistent_save")
            save_success = state.save_step_data_to_json(state.step_table_json, {
                'source': 'ai_step_modification',
                'modified_step_no': step.get('step_no'),
                'modification_timestamp': datetime.now().isoformat()
            })
            if save_success:
                debug("Successfully saved modified AI steps to persistent storage",
                      stage="hybrid_editing", operation="persistent_save_success")
            else:
                debug("Failed to save modified AI steps to persistent storage",
                      stage="hybrid_editing", operation="persistent_save_failure")

    # Show success with any warnings
    st.success(success_message)
    if validation_result["warnings"]:
        for warning in validation_result["warnings"]:
            st.warning(f"⚠️ {warning}")
    if validation_result["suggestions"]:
        for suggestion in validation_result["suggestions"]:
            st.info(f"💡 {suggestion}")

    st.rerun()
    return True


# Old AI step edit form removed - replaced by unified edit form


def _render_step_edit_form(state, step: Dict[str, Any], insertion_point: str, step_index: int) -> bool:
    """Render form for editing an existing manual step."""
    with st.form(key=f"edit_form_{insertion_point}_{step_index}"):
        st.markdown("**✏️ Edit Manual Step**")

        col1, col2 = st.columns(2)

        with col1:
            action = st.text_input("Action", value=step.get("action", ""))
            locator_strategy = st.selectbox("Locator Strategy",
                                          ["", "css", "xpath", "id", "name", "aria"],
                                          index=["", "css", "xpath", "id", "name", "aria"].index(step.get("locator_strategy", "")))
            locator = st.text_input("Locator", value=step.get("locator", ""))

        with col2:
            test_data_param = st.text_input("Test Data", value=step.get("test_data_param", ""))
            expected_result = st.text_input("Expected Result", value=step.get("expected_result", ""))
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=300,
                                    value=int(step.get("timeout", 10)))

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 Save Changes"):
                # Update step
                step.update({
                    "action": action,
                    "locator_strategy": locator_strategy,
                    "locator": locator,
                    "test_data_param": test_data_param,
                    "expected_result": expected_result,
                    "timeout": timeout,
                    "_modified_at": datetime.now().isoformat()
                })

                # Clear editing state
                del st.session_state[f"editing_step_{insertion_point}_{step_index}"]
                st.success("✏️ Manual step updated successfully")
                st.rerun()
                return True

        with col2:
            if st.form_submit_button("❌ Cancel"):
                del st.session_state[f"editing_step_{insertion_point}_{step_index}"]
                st.rerun()
                return False

    return False


def _render_step_preview(state):
    """Render preview of combined AI and manual steps."""
    st.markdown("#### 👁️ Combined Test Flow Preview")

    if not state.ai_generated_steps:
        st.warning("No AI-generated steps available.")
        return

    # Merge steps for preview
    merged_steps, markdown_table, _ = merge_ai_and_manual_steps(
        state.ai_generated_steps,
        state.step_insertion_points
    )

    if not merged_steps:
        st.info("No steps to preview.")
        return

    # Update combined step table in state
    state.combined_step_table = merged_steps

    # Display step count and modification status
    ai_count = len(state.ai_generated_steps)
    modified_ai_count = sum(1 for step in state.ai_generated_steps if step.get('_ai_step_modified', False))
    manual_count = sum(len(steps) for steps in state.step_insertion_points.values())
    total_count = len(merged_steps)

    # Debug: Log step counts and AI step details for troubleshooting
    debug(f"Preview step counts - AI: {ai_count}, Modified AI: {modified_ai_count}, Manual: {manual_count}, Total: {total_count}",
          stage="hybrid_editing", operation="preview_step_count",
          context={'ai_count': ai_count, 'modified_ai_count': modified_ai_count,
                  'manual_count': manual_count, 'total_count': total_count})

    # Debug: Log details of AI steps to verify modifications
    for i, step in enumerate(state.ai_generated_steps):
        is_modified = step.get('_ai_step_modified', False)
        debug(f"AI Step {i+1}: modified={is_modified}, action='{step.get('action', 'N/A')}'",
              stage="hybrid_editing", operation="preview_ai_step_detail",
              context={'step_index': i, 'step_no': step.get('step_no'), 'is_modified': is_modified,
                      'action': step.get('action'), 'expected_result': step.get('expected_result')})

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("🤖 AI Steps", ai_count)
    with col2:
        st.metric("🔄 Modified AI", modified_ai_count)
    with col3:
        st.metric("✏️ Manual Steps", manual_count)
    with col4:
        st.metric("📊 Total Steps", total_count)

    # Show conflict detection results
    if state.ai_generated_steps or state.step_insertion_points:
        conflict_result = _resolve_step_conflicts(state.ai_generated_steps, state.step_insertion_points)

        if conflict_result["conflicts_found"]:
            st.warning("⚠️ **Conflicts Detected:**")
            for conflict in conflict_result["conflicts"]:
                st.write(f"• {conflict}")
            st.info("**Auto-Resolutions:**")
            for resolution in conflict_result["resolutions"]:
                st.write(f"• {resolution}")

        if conflict_result["warnings"]:
            with st.expander("💡 Flow Recommendations", expanded=False):
                for warning in conflict_result["warnings"]:
                    st.info(f"• {warning}")

    # Display combined table
    if markdown_table:
        st.markdown("**Combined Step Table:**")
        # Use responsive table component for better display
        from ui_components.responsive_table_display import render_responsive_step_table
        # Call with only the supported parameters
        render_responsive_step_table(markdown_table, show_copy_button=False)

    # Export options
    st.markdown("#### 📤 Export Options")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("📋 Copy Combined Steps", key="copy_combined_steps"):
            st.session_state['clipboard_content'] = markdown_table
            st.success("✅ Combined steps copied to clipboard!")

    with col2:
        if st.button("💾 Apply to Test Case", key="apply_combined_steps"):
            # Update the main step table with combined steps
            state.step_table_json = merged_steps
            state.step_table_markdown = markdown_table
            state.combined_step_table = merged_steps

            # Update total_steps to reflect the new combined step count
            state.total_steps = len(merged_steps)

            # Reset step progress to start from the beginning with new step count
            state.current_step_index = 0
            state.all_steps_done = False

            # Clear any step-specific state that might be invalid with new steps
            # This is crucial for fixing the data inconsistency issue
            state.selected_step_table_entry = None
            state.selected_step = None  # Clear this too to force re-selection
            state.step_elements = []
            state.step_matches = {}
            state.element_matches = {}

            # Clear any cached step analysis that might be outdated
            if hasattr(state, 'llm_step_analysis'):
                state.llm_step_analysis = None

            # Regenerate step table analysis for the new combined steps
            try:
                from core.ai_helpers import analyze_step_table
                step_table_analysis = analyze_step_table((markdown_table, merged_steps))
                state.step_table_analysis = step_table_analysis
                debug(f"Regenerated step table analysis after applying combined steps: requires_ui_elements={step_table_analysis.get('requires_ui_elements', True)}",
                      stage="hybrid_editing", operation="analysis_regeneration",
                      context={'requires_ui_elements': step_table_analysis.get('requires_ui_elements', True),
                               'merged_steps_count': len(merged_steps)})
            except Exception as e:
                debug(f"Error regenerating step table analysis: {e}",
                      stage="hybrid_editing", operation="analysis_error",
                      context={'error_message': str(e), 'error_type': type(e).__name__})
                # Set to None so it will be regenerated when needed
                state.step_table_analysis = None

            debug(f"Applied combined steps to test case: {len(merged_steps)} steps total",
                  stage="hybrid_editing", operation="steps_applied",
                  context={'total_steps': len(merged_steps), 'ai_steps': len(state.ai_generated_steps), 'manual_steps': manual_count})
            debug("Cleared step-specific state to ensure consistency with new step structure",
                  stage="hybrid_editing", operation="state_cleanup",
                  context={'cleared_fields': ['selected_step_table_entry', 'selected_step', 'step_elements', 'step_matches', 'element_matches']})

            # Save the combined steps to persistent JSON storage
            debug("Saving combined steps to persistent JSON storage",
                  stage="hybrid_editing", operation="json_save_start",
                  context={'total_steps': total_count, 'source': 'hybrid_editing_apply'})
            save_success = state.save_step_data_to_json(merged_steps, {
                'source': 'hybrid_editing_apply',
                'ai_steps_count': len(state.ai_generated_steps),
                'manual_steps_count': manual_count,
                'total_steps_count': total_count,
                'insertion_points': list(state.step_insertion_points.keys()),
                'apply_timestamp': datetime.now().isoformat()
            })

            if save_success:
                debug("Successfully saved combined steps to JSON storage",
                      stage="hybrid_editing", operation="json_save_success",
                      context={'total_steps': total_count})
            else:
                debug("Failed to save combined steps to JSON storage",
                      stage="hybrid_editing", operation="json_save_error",
                      context={'total_steps': total_count})

            st.success("✅ Combined steps applied to test case!")
            st.rerun()


def _render_step_validation(state):
    """Render step flow validation results."""
    st.markdown("#### ✅ Step Flow Validation")

    if not state.ai_generated_steps:
        st.warning("No AI-generated steps available for validation.")
        return

    # Merge steps for validation
    merged_steps, _, _ = merge_ai_and_manual_steps(
        state.ai_generated_steps,
        state.step_insertion_points
    )

    if not merged_steps:
        st.info("No steps to validate.")
        return

    # Run comprehensive validation
    try:
        from helpers.step_validation import validate_combined_steps, get_validation_summary

        validation_result = validate_combined_steps(merged_steps)
        validation_dict = validation_result.to_dict()

        # Display validation summary
        summary = get_validation_summary(validation_result)
        if validation_dict["is_valid"]:
            st.success(f"✅ {summary}")
        else:
            st.error(f"❌ {summary}")

        # Create tabs for different types of issues
        if validation_dict["total_issues"] > 0 or validation_dict["total_suggestions"] > 0:
            error_tab, warning_tab, perf_tab, suggest_tab = st.tabs([
                f"🚨 Errors ({len(validation_dict['errors'])})",
                f"⚠️ Warnings ({len(validation_dict['warnings'])})",
                f"⚡ Performance ({len(validation_dict['performance_issues'])})",
                f"💡 Suggestions ({len(validation_dict['suggestions']) + len(validation_dict['best_practices'])})"
            ])

            # Display errors
            with error_tab:
                if validation_dict["errors"]:
                    for error in validation_dict["errors"]:
                        step_info = f" (Step {error['step_no']})" if error.get('step_no') else ""
                        st.error(f"• {error['message']}{step_info}")
                else:
                    st.success("✅ No errors found!")

            # Display warnings
            with warning_tab:
                if validation_dict["warnings"]:
                    for warning in validation_dict["warnings"]:
                        step_info = f" (Step {warning['step_no']})" if warning.get('step_no') else ""
                        st.warning(f"• {warning['message']}{step_info}")
                else:
                    st.success("✅ No warnings!")

            # Display performance issues
            with perf_tab:
                if validation_dict["performance_issues"]:
                    for issue in validation_dict["performance_issues"]:
                        step_info = f" (Step {issue['step_no']})" if issue.get('step_no') else ""
                        impact_emoji = "🔴" if issue.get('impact') == "high" else "🟡" if issue.get('impact') == "medium" else "🟢"
                        st.warning(f"{impact_emoji} {issue['message']}{step_info}")
                else:
                    st.success("✅ No performance issues!")

            # Display suggestions and best practices
            with suggest_tab:
                if validation_dict["suggestions"]:
                    st.markdown("**💡 Improvement Suggestions:**")
                    for suggestion in validation_dict["suggestions"]:
                        step_info = f" (Step {suggestion['step_no']})" if suggestion.get('step_no') else ""
                        st.info(f"• {suggestion['message']}{step_info}")

                if validation_dict["best_practices"]:
                    st.markdown("**📋 Best Practice Recommendations:**")
                    for practice in validation_dict["best_practices"]:
                        step_info = f" (Step {practice['step_no']})" if practice.get('step_no') else ""
                        st.info(f"• {practice['message']}{step_info}")

                if not validation_dict["suggestions"] and not validation_dict["best_practices"]:
                    st.success("✅ No suggestions - your test follows best practices!")

        else:
            st.success("🎉 Perfect! No issues found. Your test flow looks excellent.")

    except ImportError as e:
        st.error(f"❌ Failed to load validation module: {e}")
        # Fallback to basic validation
        _render_basic_validation(merged_steps)


def _render_basic_validation(merged_steps: List[Dict[str, Any]]):
    """Render basic validation as fallback when advanced validation is not available."""
    st.markdown("**Basic Validation (Fallback Mode)**")

    # Basic checks
    has_navigation = any(
        "navigate" in step.get("action", "").lower()
        for step in merged_steps
    )

    has_assertions = any(
        step.get("step_type") == "assertion" or step.get("assertion_type") not in ["", "no_error"]
        for step in merged_steps
    )

    has_cleanup = any(
        step.get("step_type") == "teardown"
        for step in merged_steps
    )

    # Display basic results
    col1, col2, col3 = st.columns(3)

    with col1:
        if has_navigation:
            st.success("✅ Navigation steps found")
        else:
            st.warning("⚠️ No navigation steps")

    with col2:
        if has_assertions:
            st.success("✅ Assertion steps found")
        else:
            st.warning("⚠️ No assertion steps")

    with col3:
        if has_cleanup:
            st.success("✅ Cleanup steps found")
        else:
            st.info("💡 Consider adding cleanup steps")

    st.info(f"📊 Total steps: {len(merged_steps)}")


def _add_manual_step_to_state(state, step: Dict[str, Any], insertion_point: str):
    """Add a manual step to the state manager."""
    if insertion_point not in state.step_insertion_points:
        state.step_insertion_points[insertion_point] = []

    state.step_insertion_points[insertion_point].append(step)
    debug(f"Added manual step to insertion point {insertion_point}: {step.get('action', 'Unknown')}",
          stage="hybrid_editing", operation="manual_step_added",
          context={'insertion_point': insertion_point, 'action': step.get('action', 'Unknown'), 'step_no': step.get('step_no', 'N/A')})


def _get_next_step_number(state, insertion_point: str) -> str:
    """Generate the next step number for a manual step."""
    # Count existing steps at this insertion point
    existing_count = len(state.step_insertion_points.get(insertion_point, []))

    # Generate a temporary step number (will be renumbered during merge)
    return f"M{existing_count + 1}"


def _get_ai_step_data_for_editing(state, step_number: str) -> Optional[Dict[str, Any]]:
    """
    Extract AI step data for manual editing when user selects an AI step number.

    Args:
        state: StateManager instance
        step_number: The AI step number (e.g., "1", "2", "3")

    Returns:
        Dict containing AI step data or None if not found
    """
    if not state.ai_generated_steps:
        return None

    # Find the AI step with the matching step number
    for ai_step in state.ai_generated_steps:
        if ai_step.get("step_no") == step_number:
            return {
                "action": ai_step.get("action", ""),
                "locator_strategy": ai_step.get("locator_strategy", ""),
                "locator": ai_step.get("locator", ""),
                "test_data_param": ai_step.get("test_data_param", ""),
                "expected_result": ai_step.get("expected_result", ""),
                "timeout": ai_step.get("timeout", 10),
                "step_type": ai_step.get("step_type", "ui"),
                "assertion_type": ai_step.get("assertion_type", "no_error"),
                "condition": ai_step.get("condition", ""),
                "step_description": ai_step.get("step_description", ""),
                "_original_ai_step_no": step_number,
                "_converting_from_ai": True
            }

    return None


def _is_ai_step_number(insertion_point: str, state) -> bool:
    """
    Check if the insertion point is an AI step number (for direct replacement).

    Args:
        insertion_point: The selected insertion point
        state: StateManager instance

    Returns:
        bool: True if insertion point is an AI step number
    """
    if not state.ai_generated_steps:
        return False

    # Check if insertion point matches any AI step number
    ai_step_numbers = [step.get("step_no", "") for step in state.ai_generated_steps]
    return insertion_point in ai_step_numbers


def _convert_ai_step_to_manual(state, step_number: str, manual_step_data: Dict[str, Any]) -> bool:
    """
    Convert an AI step to a manual step by removing it from AI list and adding to manual steps.

    Args:
        state: StateManager instance
        step_number: The AI step number to convert
        manual_step_data: The manual step data to replace the AI step

    Returns:
        bool: True if conversion was successful
    """
    if not state.ai_generated_steps:
        return False

    # Find and remove the AI step
    ai_step_to_remove = None
    for i, ai_step in enumerate(state.ai_generated_steps):
        if ai_step.get("step_no") == step_number:
            ai_step_to_remove = i
            break

    if ai_step_to_remove is None:
        debug(f"AI step {step_number} not found for conversion",
              stage="hybrid_editing", operation="ai_to_manual_conversion_failed")
        return False

    # Remove the AI step
    removed_ai_step = state.ai_generated_steps.pop(ai_step_to_remove)

    # Mark the manual step with conversion metadata
    manual_step_data.update({
        "_is_manual": True,
        "_is_ai_generated": False,
        "_converted_from_ai": True,
        "_original_ai_step_no": step_number,
        "_conversion_timestamp": datetime.now().isoformat(),
        "_original_ai_data": removed_ai_step.copy()
    })

    # Add to manual steps at a special insertion point for converted steps
    conversion_insertion_point = f"converted_{step_number}"
    if conversion_insertion_point not in state.step_insertion_points:
        state.step_insertion_points[conversion_insertion_point] = []

    state.step_insertion_points[conversion_insertion_point].append(manual_step_data)

    debug(f"Successfully converted AI step {step_number} to manual step",
          stage="hybrid_editing", operation="ai_to_manual_conversion_success",
          context={'step_number': step_number, 'action': manual_step_data.get('action', 'Unknown')})

    return True


def enable_hybrid_editing(state) -> bool:
    """
    Enable hybrid editing mode for the current test case.

    Args:
        state: StateManager instance

    Returns:
        bool: True if hybrid editing was enabled successfully
    """
    # Try to load step data if not available
    if not state.step_table_json:
        try:
            step_table_json = state.get_effective_step_table()
            debug("Loaded step data from JSON storage for enabling hybrid editing",
                  stage="hybrid_editing", operation="enable_data_loading",
                  context={'steps_count': len(step_table_json)})
        except ValueError as e:
            debug(f"Could not load step data for enabling hybrid editing: {e}",
                  stage="hybrid_editing", operation="enable_data_loading_error",
                  context={'error': str(e)})
            return False

    if not state.step_table_json:
        return False

    state.hybrid_editing_enabled = True
    state.ai_generated_steps = state.step_table_json.copy()

    # Mark AI steps with origin tracking but allow editing
    for step in state.ai_generated_steps:
        step["_is_ai_generated"] = True
        step["_is_locked"] = False  # Allow editing in hybrid mode
        step["_original_ai_step"] = True  # Track original AI origin
        step["_ai_step_modified"] = False  # Track if step has been modified

    debug("Enabled hybrid editing mode",
          stage="hybrid_editing", operation="mode_enabled",
          context={'ai_steps_count': len(state.ai_generated_steps) if state.ai_generated_steps else 0})
    return True


def _validate_ai_step_modification(step: Dict[str, Any], original_step: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Validate modifications to an AI-generated step.

    Args:
        step: The modified step data
        original_step: The original AI step data (if available)

    Returns:
        Dict containing validation results
    """
    validation_result = {
        "is_valid": True,
        "warnings": [],
        "errors": [],
        "suggestions": []
    }

    # Required field validation
    required_fields = ["action", "expected_result"]
    for field in required_fields:
        if not step.get(field, "").strip():
            validation_result["errors"].append(f"'{field}' is required and cannot be empty")
            validation_result["is_valid"] = False

    # Locator validation
    locator_strategy = step.get("locator_strategy", "")
    locator = step.get("locator", "")

    if locator_strategy and locator_strategy not in ["", "none", "url"]:
        if not locator.strip():
            validation_result["errors"].append(f"Locator value is required when using '{locator_strategy}' strategy")
            validation_result["is_valid"] = False

    # Timeout validation
    timeout = step.get("timeout", 10)
    if not isinstance(timeout, (int, float)) or timeout < 1 or timeout > 300:
        validation_result["warnings"].append("Timeout should be between 1 and 300 seconds")

    # Check for significant changes from original AI step
    if original_step:
        significant_changes = []
        critical_fields = ["action", "locator_strategy", "locator", "step_type"]

        for field in critical_fields:
            if step.get(field) != original_step.get(field):
                significant_changes.append(field)

        if significant_changes:
            validation_result["warnings"].append(
                f"Significant changes detected in: {', '.join(significant_changes)}. "
                "This may affect AI optimization."
            )

    # Step flow validation
    step_type = step.get("step_type", "ui")
    action = step.get("action", "").lower()

    if step_type == "navigation" and "navigate" not in action:
        validation_result["suggestions"].append("Consider using 'navigate' in action for navigation steps")

    if step_type == "assertion" and not step.get("assertion_type"):
        validation_result["warnings"].append("Assertion type should be specified for assertion steps")

    return validation_result


def _resolve_step_conflicts(ai_steps: List[Dict[str, Any]], manual_steps: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
    """
    Detect and resolve conflicts between modified AI steps and manual steps.

    Args:
        ai_steps: List of AI steps (potentially modified)
        manual_steps: Dictionary of manual steps by insertion point

    Returns:
        Dict containing conflict resolution results
    """
    conflict_result = {
        "conflicts_found": False,
        "conflicts": [],
        "resolutions": [],
        "warnings": []
    }

    # Check for duplicate step numbers
    all_step_numbers = []

    # Collect AI step numbers
    for step in ai_steps:
        step_no = step.get("step_no")
        if step_no:
            all_step_numbers.append(("ai", step_no, step))

    # Collect manual step numbers
    for insertion_point, steps in manual_steps.items():
        for step in steps:
            step_no = step.get("step_no")
            if step_no:
                all_step_numbers.append(("manual", step_no, step))

    # Check for duplicates
    step_numbers = [item[1] for item in all_step_numbers]
    duplicates = set([x for x in step_numbers if step_numbers.count(x) > 1])

    if duplicates:
        conflict_result["conflicts_found"] = True
        for dup in duplicates:
            conflict_result["conflicts"].append(f"Duplicate step number: {dup}")
            conflict_result["resolutions"].append(f"Step numbers will be automatically renumbered during merge")

    # Check for logical flow issues
    navigation_steps = []
    assertion_steps = []

    for step_type, step_no, step in all_step_numbers:
        if step.get("step_type") == "navigation":
            navigation_steps.append((step_no, step))
        elif step.get("step_type") == "assertion":
            assertion_steps.append((step_no, step))

    # Warn if no navigation steps
    if not navigation_steps:
        conflict_result["warnings"].append("No navigation steps found. Consider adding initial navigation.")

    # Warn if no assertions
    if not assertion_steps:
        conflict_result["warnings"].append("No assertion steps found. Consider adding result verification.")

    return conflict_result


def disable_hybrid_editing(state):
    """Disable hybrid editing mode and clear manual steps."""
    state.hybrid_editing_enabled = False
    state.step_insertion_points.clear()
    state.combined_step_table = None
    debug("Disabled hybrid editing mode",
          stage="hybrid_editing", operation="mode_disabled",
          context={'cleared_insertion_points': len(state.step_insertion_points)})
