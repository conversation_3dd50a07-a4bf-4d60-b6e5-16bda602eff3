"""
Delete operations for the GretahAI CaseForge database system.

This module handles deletion operations for test cases, test runs, and related data
with proper safety checks and admin authentication where required.
"""

import sqlite3
from datetime import datetime
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock
from .admin_operations import verify_admin_password


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_test_run(database_path, test_run_id):
    """
    Deletes a test run and all associated test cases and test steps.
    
    This function performs a cascading delete operation, removing the test run
    and all related data including test cases and test steps. It maintains
    referential integrity by deleting in proper order.

    Args:
        database_path (str): Absolute path to the SQLite database file
        test_run_id (int): ID of the test run to delete

    Returns:
        bool: True if deletion successful, False otherwise

    Process Flow:
        1. Establishes database connection
        2. Begins transaction for atomic operation
        3. Deletes test steps for all test cases in the run
        4. Deletes test cases associated with the run
        5. Deletes the test run record
        6. Commits transaction or rolls back on error

    Example:
        success = delete_test_run(db_path, 123)
        if success:
            print("Test run deleted successfully")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # First, get all test case IDs for this test run
        cursor.execute("SELECT id FROM test_cases WHERE test_run_id = ?", (test_run_id,))
        test_case_ids = [row[0] for row in cursor.fetchall()]
        
        # Delete test steps for all test cases in this run
        if test_case_ids:
            placeholders = ','.join('?' * len(test_case_ids))
            cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
            print(f"Deleted test steps for {len(test_case_ids)} test cases")
        
        # Delete test cases
        cursor.execute("DELETE FROM test_cases WHERE test_run_id = ?", (test_run_id,))
        deleted_cases = cursor.rowcount
        print(f"Deleted {deleted_cases} test cases")
        
        # Delete the test run
        cursor.execute("DELETE FROM test_runs WHERE id = ?", (test_run_id,))
        deleted_runs = cursor.rowcount
        
        if deleted_runs == 0:
            print(f"No test run found with ID {test_run_id}")
            conn.rollback()
            return False
            
        conn.commit()
        print(f"Successfully deleted test run {test_run_id}")
        return True
        
    except sqlite3.Error as e:
        print(f"Error deleting test run: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_test_case(database_path, test_case_id):
    """
    Deletes a specific test case and all its associated test steps.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        test_case_id (str): Test case ID to delete (e.g., "TC_001")

    Returns:
        bool: True if deletion successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Get the database ID for this test case
        cursor.execute("SELECT id FROM test_cases WHERE test_case_id = ?", (test_case_id,))
        result = cursor.fetchone()
        
        if not result:
            print(f"No test case found with ID {test_case_id}")
            conn.rollback()
            return False
            
        db_test_case_id = result[0]
        
        # Delete test steps
        cursor.execute("DELETE FROM test_steps WHERE test_case_id = ?", (db_test_case_id,))
        deleted_steps = cursor.rowcount
        print(f"Deleted {deleted_steps} test steps")
        
        # Delete test case
        cursor.execute("DELETE FROM test_cases WHERE id = ?", (db_test_case_id,))
        deleted_cases = cursor.rowcount
        
        conn.commit()
        print(f"Successfully deleted test case {test_case_id}")
        return True
        
    except sqlite3.Error as e:
        print(f"Error deleting test case: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_edited_test_cases(database_path, jira_id=None, admin_password=None):
    """
    Deletes test cases marked as edited with optional admin authentication.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str, optional): JIRA ID to filter by. If None, affects all JIRA IDs.
        admin_password (str, optional): Admin password for authentication

    Returns:
        tuple: (success: bool, message: str, deleted_count: int)
    """
    # Verify admin password if provided
    if admin_password and not verify_admin_password(database_path, admin_password):
        return False, "Invalid admin password", 0
        
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Build query based on whether jira_id is specified
        if jira_id:
            cursor.execute("SELECT id FROM test_cases WHERE is_edited = 1 AND jira_id = ?", (jira_id,))
        else:
            cursor.execute("SELECT id FROM test_cases WHERE is_edited = 1")
            
        test_case_ids = [row[0] for row in cursor.fetchall()]
        
        if not test_case_ids:
            conn.rollback()
            message = f"No edited test cases found{f' for JIRA ID {jira_id}' if jira_id else ''}"
            return True, message, 0
            
        # Delete test steps for edited test cases
        placeholders = ','.join('?' * len(test_case_ids))
        cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
        deleted_steps = cursor.rowcount
        
        # Delete edited test cases
        if jira_id:
            cursor.execute("DELETE FROM test_cases WHERE is_edited = 1 AND jira_id = ?", (jira_id,))
        else:
            cursor.execute("DELETE FROM test_cases WHERE is_edited = 1")
            
        deleted_cases = cursor.rowcount
        
        conn.commit()
        
        message = f"Successfully deleted {deleted_cases} edited test cases and {deleted_steps} test steps"
        if jira_id:
            message += f" for JIRA ID {jira_id}"
            
        print(message)
        return True, message, deleted_cases
        
    except sqlite3.Error as e:
        error_msg = f"Database error deleting edited test cases: {e}"
        print(error_msg)
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, error_msg, 0
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_all_edited_test_cases(database_path, admin_password=None):
    """
    Deletes all test cases marked as edited across all JIRA IDs.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        admin_password (str, optional): Admin password for authentication

    Returns:
        tuple: (success: bool, message: str, deleted_count: int)
    """
    return delete_edited_test_cases(database_path, jira_id=None, admin_password=admin_password)


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_specific_test_cases(database_path, start_id=539, end_id=578):
    """
    Deletes test cases within a specific ID range.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        start_id (int): Starting database ID (inclusive)
        end_id (int): Ending database ID (inclusive)

    Returns:
        bool: True if deletion successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Delete test steps for test cases in the range
        cursor.execute(
            "DELETE FROM test_steps WHERE test_case_id BETWEEN ? AND ?",
            (start_id, end_id)
        )
        deleted_steps = cursor.rowcount
        
        # Delete test cases in the range
        cursor.execute(
            "DELETE FROM test_cases WHERE id BETWEEN ? AND ?",
            (start_id, end_id)
        )
        deleted_cases = cursor.rowcount
        
        conn.commit()
        print(f"Deleted {deleted_cases} test cases and {deleted_steps} test steps in range {start_id}-{end_id}")
        return True
        
    except sqlite3.Error as e:
        print(f"Error deleting specific test cases: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_by_timestamp(database_path, timestamp_pattern):
    """
    Deletes test cases matching a timestamp pattern.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        timestamp_pattern (str): Timestamp pattern to match (SQL LIKE pattern)

    Returns:
        bool: True if deletion successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Get test case IDs matching the timestamp pattern
        cursor.execute("SELECT id FROM test_cases WHERE timestamp LIKE ?", (timestamp_pattern,))
        test_case_ids = [row[0] for row in cursor.fetchall()]
        
        if not test_case_ids:
            print(f"No test cases found matching timestamp pattern: {timestamp_pattern}")
            conn.rollback()
            return True
            
        # Delete test steps
        placeholders = ','.join('?' * len(test_case_ids))
        cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
        deleted_steps = cursor.rowcount
        
        # Delete test cases
        cursor.execute("DELETE FROM test_cases WHERE timestamp LIKE ?", (timestamp_pattern,))
        deleted_cases = cursor.rowcount
        
        conn.commit()
        print(f"Deleted {deleted_cases} test cases and {deleted_steps} test steps matching timestamp pattern")
        return True
        
    except sqlite3.Error as e:
        print(f"Error deleting by timestamp: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_duplicate_test_cases(database_path, jira_id=None):
    """
    Deletes duplicate test cases, keeping only the most recent version.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str, optional): JIRA ID to filter by. If None, affects all JIRA IDs.

    Returns:
        bool: True if deletion successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Find duplicate test cases (same test_case_id and jira_id)
        if jira_id:
            query = """
                SELECT test_case_id, jira_id, MAX(timestamp) as latest_timestamp
                FROM test_cases
                WHERE jira_id = ?
                GROUP BY test_case_id, jira_id
                HAVING COUNT(*) > 1
            """
            cursor.execute(query, (jira_id,))
        else:
            query = """
                SELECT test_case_id, jira_id, MAX(timestamp) as latest_timestamp
                FROM test_cases
                GROUP BY test_case_id, jira_id
                HAVING COUNT(*) > 1
            """
            cursor.execute(query)
            
        duplicates = cursor.fetchall()
        
        if not duplicates:
            print("No duplicate test cases found")
            conn.rollback()
            return True
            
        deleted_count = 0
        
        for duplicate in duplicates:
            test_case_id = duplicate['test_case_id']
            jira_id_val = duplicate['jira_id']
            latest_timestamp = duplicate['latest_timestamp']
            
            # Get IDs of older duplicates
            cursor.execute(
                """SELECT id FROM test_cases 
                   WHERE test_case_id = ? AND jira_id = ? AND timestamp < ?""",
                (test_case_id, jira_id_val, latest_timestamp)
            )
            
            old_duplicate_ids = [row[0] for row in cursor.fetchall()]
            
            if old_duplicate_ids:
                # Delete test steps for old duplicates
                placeholders = ','.join('?' * len(old_duplicate_ids))
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", old_duplicate_ids)
                
                # Delete old duplicate test cases
                cursor.execute(f"DELETE FROM test_cases WHERE id IN ({placeholders})", old_duplicate_ids)
                deleted_count += len(old_duplicate_ids)
                
        conn.commit()
        print(f"Deleted {deleted_count} duplicate test cases")
        return True
        
    except sqlite3.Error as e:
        print(f"Error deleting duplicate test cases: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_test_cases_by_time_range(database_path, start_datetime, end_datetime, admin_password=None):
    """
    Deletes test cases created within a specific time range.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        start_datetime (str): Start datetime (YYYY-MM-DD HH:MM:SS format)
        end_datetime (str): End datetime (YYYY-MM-DD HH:MM:SS format)
        admin_password (str, optional): Admin password for verification

    Returns:
        tuple: (success: bool, message: str, deletion_counts: dict)
    """
    # Verify admin password if provided
    if admin_password:
        from .admin_operations import verify_admin_password
        if not verify_admin_password(database_path, admin_password):
            return False, "Invalid admin password", {}
    
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Get test case IDs in the time range
        cursor.execute(
            "SELECT id FROM test_cases WHERE timestamp BETWEEN ? AND ?",
            (start_datetime, end_datetime)
        )
        test_case_ids = [row[0] for row in cursor.fetchall()]
        
        if not test_case_ids:
            conn.rollback()
            return True, f"No test cases found in time range {start_datetime} to {end_datetime}", {}
            
        # Initialize deletion counters
        deletion_counts = {
            'test_cases': 0,
            'test_steps': 0,
            'test_runs': 0,
            'test_case_executions': 0
        }
        
        # Delete test steps
        placeholders = ','.join('?' * len(test_case_ids))
        cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
        deletion_counts['test_steps'] = cursor.rowcount
        
        # Delete test case executions if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions'")
        if cursor.fetchone():
            cursor.execute(f"DELETE FROM test_case_executions WHERE test_case_id IN ({placeholders})", test_case_ids)
            deletion_counts['test_case_executions'] = cursor.rowcount
        
        # Delete test cases
        cursor.execute(
            "DELETE FROM test_cases WHERE timestamp BETWEEN ? AND ?",
            (start_datetime, end_datetime)
        )
        deletion_counts['test_cases'] = cursor.rowcount
        
        conn.commit()
        
        # Create summary message
        summary_parts = []
        if deletion_counts['test_cases'] > 0:
            summary_parts.append(f"{deletion_counts['test_cases']} test cases")
        if deletion_counts['test_steps'] > 0:
            summary_parts.append(f"{deletion_counts['test_steps']} test steps")
        if deletion_counts['test_case_executions'] > 0:
            summary_parts.append(f"{deletion_counts['test_case_executions']} executions")
        
        summary = f"Successfully deleted {', '.join(summary_parts)} in time range {start_datetime} to {end_datetime}"
        print(summary)
        
        return True, summary, deletion_counts
        
    except sqlite3.Error as e:
        error_msg = f"Database error deleting test cases by time range: {e}"
        print(error_msg)
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, error_msg, {}
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")
                