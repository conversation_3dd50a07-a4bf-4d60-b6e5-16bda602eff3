"""
Stage 10 Gap Analysis Components for GretahAI ScriptWeaver

AI gap analysis, gap filling forms, and reanalysis workflows components.
Extracted from stage10_components.py for better maintainability.
"""

import streamlit as st
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import GRETAH standardized logging
from debug_utils import debug


def render_gap_analysis_interface(selected_template, selected_test_case):
    """
    Render the gap analysis interface that analyzes differences between template and test case.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        dict: Gap analysis results or None if analysis not completed
    """
    with st.expander("🔍 Gap Analysis", expanded=True):
        # Validate inputs first
        from core.template_helpers import validate_template_generation_inputs

        is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

        if not is_valid:
            st.error(f"❌ {error_message}")
            return None

        st.markdown("**Analyze template compatibility with your test case requirements**")

        # Check if gap analysis has been performed for this combination
        gap_analysis_key = f"gap_analysis_{selected_template.get('id', 'unknown')}_{selected_test_case.get('Test Case ID', 'unknown')}"

        # Check if gap analysis has been performed
        has_existing_analysis = gap_analysis_key in st.session_state

        # Gap analysis controls
        if has_existing_analysis:
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.info("🤖 Gap analysis completed")

            with col2:
                reanalyze_clicked = st.button("🔄 Re-analyze", use_container_width=True, type="secondary")

            with col3:
                clear_clicked = st.button("🗑️ Clear", use_container_width=True, type="secondary")

            # Handle clear button
            if clear_clicked:
                if gap_analysis_key in st.session_state:
                    del st.session_state[gap_analysis_key]
                st.success("✅ Gap analysis cleared!")
                st.rerun()
                return None

            # Handle re-analyze button
            if reanalyze_clicked:
                with st.spinner("🤖 Re-analyzing template coverage..."):
                    gap_analysis_data = _perform_gap_analysis(selected_template, selected_test_case)
                    if gap_analysis_data:
                        st.session_state[gap_analysis_key] = gap_analysis_data
                        st.success("✅ Re-analysis completed!")
                        st.rerun()
                return st.session_state.get(gap_analysis_key)
        else:
            col1, col2 = st.columns([3, 1])

            with col1:
                st.info("🤖 AI will analyze template coverage and identify missing requirements")

            with col2:
                analyze_clicked = st.button("🔍 Analyze", use_container_width=True, type="primary")

            # Perform initial gap analysis if button clicked
            if analyze_clicked:
                with st.spinner("🤖 Analyzing template coverage..."):
                    gap_analysis_data = _perform_gap_analysis(selected_template, selected_test_case)
                    if gap_analysis_data:
                        st.session_state[gap_analysis_key] = gap_analysis_data
                        st.rerun()
                return None

        # Display existing gap analysis results if available
        if gap_analysis_key in st.session_state:
            gap_analysis_data = st.session_state[gap_analysis_key]
            _render_gap_analysis_results(gap_analysis_data)
            return gap_analysis_data

        return None


def render_gap_filling_form(gap_analysis_data):
    """
    Legacy function maintained for backward compatibility.
    Now redirects to the new gap handling options interface.

    Args:
        gap_analysis_data: Results from gap analysis

    Returns:
        dict: User-provided gap filling data or None
    """
    # Redirect to new gap handling options interface
    selected_option, gap_responses = render_gap_handling_options(gap_analysis_data)
    return gap_responses


def render_gap_handling_options(gap_analysis_data):
    """
    Render gap handling options interface that presents two distinct AI-powered approaches.

    Args:
        gap_analysis_data: Results from gap analysis

    Returns:
        tuple: (selected_option, gap_responses) where selected_option is 'targeted', 'inference', or None
    """
    if not gap_analysis_data or not gap_analysis_data.get('gaps_identified'):
        return None, None

    with st.expander("🎯 Gap Handling Options", expanded=True):
        st.markdown("**Choose how to handle the identified gaps:**")

        # Create option selection
        option_key = f"gap_option_{gap_analysis_data.get('analysis_timestamp', 'default')}"

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            **🎯 Option 1: Targeted Gap Filling**
            - AI generates specific questions for each gap
            - You provide exact missing information
            - More precise but requires user input
            - Best for critical missing data
            """)

            if st.button("🎯 Use Targeted Filling", use_container_width=True, type="primary", key=f"{option_key}_targeted"):
                st.session_state[f"{option_key}_selected"] = "targeted"
                st.rerun()

        with col2:
            st.markdown("""
            **🤖 Option 2: Intelligent Inference**
            - AI makes smart assumptions about gaps
            - Faster generation with minimal input
            - AI-suggested values marked in comments
            - Best for minor gaps or quick prototyping
            """)

            if st.button("🤖 Use AI Inference", use_container_width=True, type="secondary", key=f"{option_key}_inference"):
                st.session_state[f"{option_key}_selected"] = "inference"
                st.rerun()

        # Check if an option has been selected
        selected_option = st.session_state.get(f"{option_key}_selected")

        if selected_option == "targeted":
            st.success("✅ **Targeted Gap Filling** selected - AI will generate specific questions")
            gap_responses = _render_targeted_gap_filling_form(gap_analysis_data)
            return "targeted", gap_responses

        elif selected_option == "inference":
            st.success("✅ **Intelligent Inference** selected - AI will make smart assumptions")
            # For inference mode, we don't need user input, just return the option
            return "inference", None

        # No option selected yet
        st.info("👆 Please select a gap handling approach to continue")
        return None, None


def _render_gap_analysis_results(gap_analysis_data):
    """
    Render the gap analysis results display.

    Args:
        gap_analysis_data: Results from gap analysis
    """
    if not gap_analysis_data:
        return

    gaps_identified = gap_analysis_data.get('gaps_identified', False)
    gaps = gap_analysis_data.get('gaps', [])
    compatibility_score = gap_analysis_data.get('compatibility_score', 0)
    analysis_summary = gap_analysis_data.get('analysis_summary', 'No summary available')

    # Display compatibility score
    if compatibility_score >= 80:
        st.success(f"🎯 **High Compatibility**: {compatibility_score}% match")
    elif compatibility_score >= 60:
        st.warning(f"⚠️ **Medium Compatibility**: {compatibility_score}% match")
    else:
        st.error(f"❌ **Low Compatibility**: {compatibility_score}% match")

    # Display analysis summary
    st.markdown(f"**Analysis Summary:** {analysis_summary}")

    # Display gaps if any
    if gaps_identified and gaps:
        st.markdown("### 🔍 Identified Gaps")
        for i, gap in enumerate(gaps, 1):
            gap_type = gap.get('type', 'Unknown')
            gap_description = gap.get('description', 'No description')
            gap_severity = gap.get('severity', 'medium')

            # Severity-based styling
            if gap_severity == 'high':
                st.error(f"**Gap {i}: {gap_type}** - {gap_description}")
            elif gap_severity == 'medium':
                st.warning(f"**Gap {i}: {gap_type}** - {gap_description}")
            else:
                st.info(f"**Gap {i}: {gap_type}** - {gap_description}")
    else:
        st.success("✅ **No significant gaps identified** - Template appears well-suited for the target test case")


def _perform_gap_analysis(selected_template, selected_test_case):
    """
    Perform AI-powered gap analysis between template and test case.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        dict: Gap analysis results or None if analysis fails
    """
    try:
        # Import AI function inside to avoid circular imports
        from core.ai import generate_llm_response

        debug("Performing gap analysis using AI",
              stage="stage10", operation="gap_analysis_start",
              context={'template_id': selected_template.get('id', 'unknown'),
                       'test_case_id': selected_test_case.get('Test Case ID', 'unknown')})

        # Build prompt for gap analysis
        prompt = _build_gap_analysis_prompt(selected_template, selected_test_case)

        # Call Google AI for gap analysis
        debug("Calling Google AI for gap analysis",
              stage="stage10", operation="ai_api_call",
              context={'api_type': 'gap_analysis', 'model': 'gemini-2.0-flash'})
        response = generate_llm_response(
            prompt=prompt,
            model_name="gemini-2.0-flash",
            api_key=st.session_state.get('state', {}).google_api_key if hasattr(st.session_state.get('state', {}), 'google_api_key') else None,
            category="gap_analysis",
            context={
                'template_id': selected_template.get('id', 'unknown'),
                'test_case_id': selected_test_case.get('Test Case ID', 'unknown'),
                'analysis_type': 'template_compatibility'
            }
        )

        if response:
            # Parse the AI response into structured gap analysis data
            gap_analysis_data = _parse_gap_analysis_response(response)
            gap_analysis_data['analysis_timestamp'] = datetime.now().isoformat()
            debug(f"Gap analysis completed with {len(gap_analysis_data.get('gaps', []))} gaps identified",
                  stage="stage10", operation="gap_analysis_completed",
                  context={'gaps_count': len(gap_analysis_data.get('gaps', [])),
                           'compatibility_score': gap_analysis_data.get('compatibility_score', 0)})
            return gap_analysis_data
        else:
            debug("Failed to get response from AI for gap analysis",
                  stage="stage10", operation="ai_api_error",
                  context={'api_type': 'gap_analysis', 'error_type': 'no_response'})
            return _create_fallback_gap_analysis()

    except Exception as e:
        debug(f"Error performing gap analysis: {e}",
              stage="stage10", operation="gap_analysis_error",
              context={'error_message': str(e), 'error_type': type(e).__name__})
        return _create_fallback_gap_analysis(str(e))


def _render_targeted_gap_filling_form(gap_analysis_data):
    """
    Render enhanced dynamic form for targeted gap filling with AI-generated specific questions.

    Args:
        gap_analysis_data: Results from gap analysis

    Returns:
        dict: User-provided gap filling data or None
    """
    st.markdown("---")
    st.markdown("### 📝 Targeted Gap Filling")

    # Check if we need to generate targeted questions
    targeted_questions_key = f"targeted_questions_{gap_analysis_data.get('analysis_timestamp', 'default')}"

    if targeted_questions_key not in st.session_state:
        with st.spinner("🤖 Generating specific questions for each gap..."):
            targeted_questions = _generate_targeted_gap_questions(gap_analysis_data)
            if targeted_questions:
                st.session_state[targeted_questions_key] = targeted_questions
                st.rerun()
            else:
                st.error("❌ Failed to generate targeted questions. Falling back to basic form.")
                return _render_basic_gap_filling_form(gap_analysis_data)

    # Render the targeted questions form
    targeted_questions = st.session_state.get(targeted_questions_key, [])
    gap_responses = {}

    if targeted_questions:
        st.markdown("**AI has generated specific questions for each identified gap:**")

        for i, question_data in enumerate(targeted_questions):
            question = question_data.get('question', 'No question generated')
            gap_type = question_data.get('gap_type', 'general')
            gap_description = question_data.get('gap_description', 'No description')
            input_type = question_data.get('input_type', 'text_area')

            st.markdown(f"**Question {i+1}: {gap_type.title()}**")
            st.markdown(f"*{gap_description}*")

            # Render appropriate input based on AI suggestion
            response = None
            if input_type == 'text_input':
                response = st.text_input(
                    question,
                    placeholder="Enter your response...",
                    key=f"targeted_gap_response_{i}"
                )
            elif input_type == 'number_input':
                response = st.number_input(
                    question,
                    min_value=0,
                    key=f"targeted_gap_response_{i}"
                )
            elif input_type == 'selectbox':
                options = question_data.get('options', ['Yes', 'No'])
                response = st.selectbox(
                    question,
                    options,
                    key=f"targeted_gap_response_{i}"
                )
            else:  # Default to text_area
                response = st.text_area(
                    question,
                    placeholder="Provide detailed information...",
                    key=f"targeted_gap_response_{i}"
                )

            if response:
                gap_responses[f"gap_{i}"] = {
                    'type': gap_type,
                    'description': gap_description,
                    'question': question,
                    'response': str(response),
                    'input_type': input_type
                }

            st.markdown("---")

    # Return gap responses if any were provided
    if gap_responses:
        st.success(f"✅ {len(gap_responses)} gap(s) filled with targeted information")
        return gap_responses

    return None


def _generate_targeted_gap_questions(gap_analysis_data):
    """
    Generate specific targeted questions for each identified gap using AI.

    Args:
        gap_analysis_data: Results from gap analysis

    Returns:
        list: List of targeted question data or None if generation fails
    """
    try:
        # Import AI function inside to avoid circular imports
        from core.ai import generate_llm_response

        debug("Generating targeted gap questions using AI",
              stage="stage10", operation="targeted_questions_start",
              context={'gaps_count': len(gap_analysis_data.get('gaps', []))})

        gaps = gap_analysis_data.get('gaps', [])
        if not gaps:
            return []

        # Build prompt for generating targeted questions
        prompt = _build_targeted_questions_prompt(gaps)

        # Call Google AI for targeted question generation
        debug("Calling Google AI for targeted question generation",
              stage="stage10", operation="ai_api_call",
              context={'api_type': 'targeted_questions', 'model': 'gemini-2.0-flash'})
        response = generate_llm_response(
            prompt=prompt,
            model_name="gemini-2.0-flash",
            api_key=st.session_state.get('state', {}).google_api_key if hasattr(st.session_state.get('state', {}), 'google_api_key') else None,
            category="targeted_gap_questions",
            context={
                'gaps_count': len(gaps),
                'analysis_type': 'targeted_question_generation'
            }
        )

        if response:
            # Parse the AI response into structured question data
            targeted_questions = _parse_targeted_questions_response(response, gaps)
            debug(f"Generated {len(targeted_questions)} targeted questions",
                  stage="stage10", operation="targeted_questions_completed",
                  context={'questions_count': len(targeted_questions)})
            return targeted_questions
        else:
            debug("Failed to get response from AI for targeted question generation",
                  stage="stage10", operation="ai_api_error",
                  context={'api_type': 'targeted_questions', 'error_type': 'no_response'})
            return None

    except Exception as e:
        debug(f"Error generating targeted gap questions: {e}",
              stage="stage10", operation="targeted_questions_error",
              context={'error_message': str(e), 'error_type': type(e).__name__})
        return None


def _build_targeted_questions_prompt(gaps):
    """
    Build the prompt for generating targeted questions for each gap.

    Args:
        gaps: List of identified gaps

    Returns:
        str: Targeted questions generation prompt
    """
    prompt = """# Generate Targeted Questions for Gap Filling

You need to generate specific, actionable questions for each identified gap to help users provide exact missing information.

## Identified Gaps:
"""

    for i, gap in enumerate(gaps):
        gap_type = gap.get('type', 'Unknown')
        gap_description = gap.get('description', 'No description')
        gap_severity = gap.get('severity', 'medium')

        prompt += f"""
**Gap {i+1}:**
- Type: {gap_type}
- Description: {gap_description}
- Severity: {gap_severity}
"""

    prompt += """

## Instructions:

Generate a JSON array with targeted questions for each gap. Each question should:

1. **Be specific and actionable** - Ask for exact values, not general descriptions
2. **Match the gap type** - Element locators need CSS selectors, timeouts need numbers, etc.
3. **Include appropriate input type** - Choose the best UI input for the expected answer
4. **Provide options when applicable** - For selectbox inputs, include relevant choices

## Output Format:

```json
[
    {
        "gap_index": 0,
        "gap_type": "element_locator",
        "gap_description": "Missing button locator",
        "question": "What is the exact text, ID, or CSS selector for the login button?",
        "input_type": "text_input",
        "options": null
    },
    {
        "gap_index": 1,
        "gap_type": "timeout_value",
        "gap_description": "Missing timeout duration",
        "question": "How many seconds should the script wait for this element?",
        "input_type": "number_input",
        "options": null
    },
    {
        "gap_index": 2,
        "gap_type": "user_action",
        "gap_description": "Unclear user action",
        "question": "Should the script click or double-click this element?",
        "input_type": "selectbox",
        "options": ["Click", "Double-click", "Right-click"]
    }
]
```

Input types available:
- "text_input": For short text responses
- "text_area": For longer text responses
- "number_input": For numeric values
- "selectbox": For predefined options (include options array)

Make questions specific and actionable. Focus on getting exact values needed for script generation.
"""

    return prompt


def _parse_targeted_questions_response(response, original_gaps):
    """
    Parse the AI response for targeted questions into structured data.

    Args:
        response: Raw AI response text
        original_gaps: Original gaps from gap analysis

    Returns:
        list: List of parsed targeted question data
    """
    try:
        # Clean the response to extract JSON
        from core.ai_helpers import clean_llm_response
        cleaned_response = clean_llm_response(response)

        # Try to parse as JSON
        try:
            targeted_questions = json.loads(cleaned_response)
            if isinstance(targeted_questions, list):
                debug(f"Successfully parsed {len(targeted_questions)} targeted questions",
                      stage="stage10", operation="response_parsing_success",
                      context={'questions_count': len(targeted_questions), 'response_type': 'targeted_questions'})
                return targeted_questions
        except json.JSONDecodeError:
            debug("Failed to parse targeted questions as JSON, creating fallback",
                  stage="stage10", operation="response_parsing_error",
                  context={'response_type': 'targeted_questions', 'error_type': 'json_decode_error'})

        # Fallback to creating basic questions
        return _create_fallback_targeted_questions(original_gaps)

    except Exception as e:
        debug(f"Error parsing targeted questions response: {e}",
              stage="stage10", operation="response_parsing_error",
              context={'error_message': str(e), 'error_type': type(e).__name__, 'response_type': 'targeted_questions'})
        return _create_fallback_targeted_questions(original_gaps)


def _create_fallback_targeted_questions(original_gaps):
    """
    Create fallback targeted questions when AI generation fails.

    Args:
        original_gaps: Original gaps from gap analysis

    Returns:
        list: List of fallback targeted question data
    """
    fallback_questions = []

    for i, gap in enumerate(original_gaps):
        gap_type = gap.get('type', 'general')
        gap_description = gap.get('description', 'Missing information')

        # Create basic question based on gap type
        if 'locator' in gap_type.lower() or 'element' in gap_type.lower():
            question = f"What is the CSS selector, ID, or text for this element?"
            input_type = "text_input"
        elif 'timeout' in gap_type.lower() or 'wait' in gap_type.lower():
            question = f"How many seconds should the script wait?"
            input_type = "number_input"
        elif 'action' in gap_type.lower():
            question = f"What action should be performed?"
            input_type = "selectbox"
            options = ["Click", "Type", "Select", "Navigate"]
        else:
            question = f"Please provide the missing information for: {gap_description}"
            input_type = "text_area"

        fallback_questions.append({
            'gap_index': i,
            'gap_type': gap_type,
            'gap_description': gap_description,
            'question': question,
            'input_type': input_type,
            'options': options if input_type == "selectbox" else None
        })

    debug(f"Created {len(fallback_questions)} fallback targeted questions",
          stage="stage10", operation="fallback_creation",
          context={'questions_count': len(fallback_questions), 'fallback_type': 'targeted_questions'})
    return fallback_questions


def _render_basic_gap_filling_form(gap_analysis_data):
    """
    Render basic gap filling form (legacy implementation as fallback).

    Args:
        gap_analysis_data: Results from gap analysis

    Returns:
        dict: User-provided gap filling data or None
    """
    st.markdown("---")
    st.markdown("### 📝 Basic Gap Filling")

    gaps = gap_analysis_data.get('gaps', [])
    if not gaps:
        st.info("No gaps to fill.")
        return None

    gap_responses = {}

    st.markdown("**Please provide information for the identified gaps:**")

    for i, gap in enumerate(gaps):
        gap_type = gap.get('type', 'Unknown')
        gap_description = gap.get('description', 'No description')

        st.markdown(f"**Gap {i+1}: {gap_type}**")
        st.markdown(f"*{gap_description}*")

        response = st.text_area(
            f"Response for Gap {i+1}",
            placeholder="Provide the missing information...",
            key=f"basic_gap_response_{i}"
        )

        if response:
            gap_responses[f"gap_{i}"] = {
                'type': gap_type,
                'description': gap_description,
                'response': response
            }

        st.markdown("---")

    if gap_responses:
        st.success(f"✅ {len(gap_responses)} gap(s) filled")
        return gap_responses

    return None


def _build_gap_analysis_prompt(selected_template, selected_test_case):
    """
    Build the prompt for AI gap analysis.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        str: Gap analysis prompt
    """
    template_content = selected_template.get('content', 'No content available')
    template_metadata = selected_template.get('metadata', {})

    tc_id = selected_test_case.get('Test Case ID', 'Unknown')
    tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
    tc_steps = selected_test_case.get('Steps', [])

    prompt = f"""# Template-Test Case Gap Analysis

Analyze the compatibility between this automation script template and the target test case. Identify any gaps, missing information, or incompatibilities that would prevent successful script generation.

## Template Information:
**Template ID:** {selected_template.get('id', 'Unknown')}
**Template Type:** {selected_template.get('type', 'Unknown')}
**Original Test Case:** {template_metadata.get('original_test_case_id', 'Unknown')}

**Template Script:**
```python
{template_content}
```

## Target Test Case:
**Test Case ID:** {tc_id}
**Objective:** {tc_objective}
**Steps Count:** {len(tc_steps)}

**Test Case Steps:**"""

    for i, step in enumerate(tc_steps, 1):
        step_description = step.get('step_description', 'No description')
        prompt += f"\n{i}. {step_description}"

    prompt += """

## Analysis Instructions:

Perform a comprehensive gap analysis and provide results in the following JSON format:

```json
{
    "compatibility_score": 85,
    "gaps_identified": true,
    "analysis_summary": "Template is mostly compatible but requires locator updates and additional validation steps.",
    "gaps": [
        {
            "type": "element_locator",
            "description": "Template uses generic button selector, target test case requires specific login button locator",
            "severity": "high",
            "impact": "Script will fail to find the correct element"
        },
        {
            "type": "validation_step",
            "description": "Target test case includes email validation not present in template",
            "severity": "medium",
            "impact": "Missing verification step could lead to false positives"
        }
    ],
    "recommendations": {
        "gap_filling_priority": "high",
        "suggested_approach": "targeted",
        "key_areas": ["element_locators", "validation_steps", "error_handling"]
    }
}
```

## Gap Types to Consider:
- **element_locator**: Missing or incompatible element selectors
- **validation_step**: Missing verification or assertion steps
- **navigation**: Different page flows or URL patterns
- **data_input**: Different form fields or input requirements
- **timeout_value**: Missing or inappropriate wait times
- **error_handling**: Missing exception handling for specific scenarios
- **user_action**: Different interaction patterns (click vs double-click, etc.)

## Scoring Guidelines:
- **90-100**: Excellent compatibility, minimal gaps
- **70-89**: Good compatibility, minor gaps
- **50-69**: Moderate compatibility, significant gaps
- **Below 50**: Poor compatibility, major rework needed

Focus on actionable gaps that can be addressed through user input or AI inference.
"""

    return prompt


def _parse_gap_analysis_response(response):
    """
    Parse the AI response for gap analysis into structured data.

    Args:
        response: Raw AI response text

    Returns:
        dict: Parsed gap analysis data
    """
    try:
        # Clean the response to extract JSON
        from core.ai_helpers import clean_llm_response
        cleaned_response = clean_llm_response(response)

        # Try to parse as JSON
        try:
            gap_data = json.loads(cleaned_response)
            if isinstance(gap_data, dict):
                debug("Successfully parsed gap analysis response",
                      stage="stage10", operation="response_parsing_success",
                      context={'response_type': 'gap_analysis'})
                return gap_data
        except json.JSONDecodeError:
            debug("Failed to parse gap analysis as JSON, creating fallback",
                  stage="stage10", operation="response_parsing_error",
                  context={'response_type': 'gap_analysis', 'error_type': 'json_decode_error'})

        # Fallback to creating basic analysis
        return _create_fallback_gap_analysis(response)

    except Exception as e:
        debug(f"Error parsing gap analysis response: {e}",
              stage="stage10", operation="response_parsing_error",
              context={'error_message': str(e), 'error_type': type(e).__name__, 'response_type': 'gap_analysis'})
        return _create_fallback_gap_analysis(str(e))


def _create_fallback_gap_analysis(response=None):
    """
    Create a simple fallback gap analysis when JSON parsing fails.

    Args:
        response: Optional raw response for context

    Returns:
        dict: Fallback gap analysis data
    """
    fallback_data = {
        'compatibility_score': 75,
        'gaps_identified': True,
        'analysis_summary': 'Basic compatibility analysis completed. Manual review recommended.',
        'gaps': [
            {
                'type': 'general',
                'description': 'Template and test case compatibility requires manual verification',
                'severity': 'medium',
                'impact': 'May require adjustments during script generation'
            }
        ],
        'recommendations': {
            'gap_filling_priority': 'medium',
            'suggested_approach': 'targeted',
            'key_areas': ['element_locators', 'validation_steps']
        }
    }

    debug("Created fallback gap analysis data",
          stage="stage10", operation="fallback_creation",
          context={'fallback_type': 'gap_analysis'})
    return fallback_data
