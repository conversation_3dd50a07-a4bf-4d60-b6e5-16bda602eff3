"""
Prompt Builder for Script Playground (Stage 10).

This module provides specialized prompt generation for creating test automation scripts
using optimized scripts as templates. It focuses on preserving the template's structure,
patterns, and best practices while adapting the content for new test cases.
"""

import logging
from typing import Dict, Any, List, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Note: Template-based generation uses pre-validated scripts from Stages 1-8
# which already have resolved locators, so locator conflict resolution is not needed here


def generate_template_based_script_prompt(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    template_structure_info: Dict[str, Any],
    website_url: str = None
) -> str:
    """
    Generate a comprehensive prompt for template-based test script generation.

    Args:
        template_script: The optimized script to use as template
        target_test_case: The test case to generate script for
        template_structure_info: Structural analysis of the template
        website_url: Target website URL

    Returns:
        str: The generated prompt for AI script generation
    """
    try:
        # Extract template information
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')

        # Extract target test case information
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')
        target_steps = target_test_case.get('Steps', [])

        # Build the prompt
        prompt = f"""# Template-Based Test Script Generation

## Task
Generate a new PyTest automation script for the target test case using the provided optimized script as a template.
Preserve the template's structure, patterns, and best practices while adapting the content for the new test case.

## Template Script Information
- **Original Test Case ID**: {template_test_case_id}
- **Template Type**: Optimized Script (proven structure and patterns)
- **Template Size**: {template_structure_info.get('total_lines', 0)} lines
- **Test Functions**: {len(template_structure_info.get('test_functions', []))}
- **Helper Functions**: {len(template_structure_info.get('helper_functions', []))}
- **Locator Strategies**: {', '.join(template_structure_info.get('locator_strategies', []))}
- **Has Browser Setup**: {template_structure_info.get('has_browser_setup', False)}
- **Has Error Handling**: {template_structure_info.get('has_error_handling', False)}
- **Has Assertions**: {template_structure_info.get('has_assertions', False)}

## Target Test Case
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}
- **Number of Steps**: {len(target_steps)}
{_format_target_steps(target_steps)}

## Template Script (Reference)
```python
{template_content}
```

## Generation Instructions

### 1. Structure Preservation
- Maintain the same overall script structure as the template
- Preserve import statements and fixture definitions
- Keep the same function organization pattern
- Maintain error handling and browser setup patterns

### 2. Content Adaptation
- Replace template test case logic with target test case requirements
- Update function names to reflect the new test case ID
- Adapt step implementations to match target test case steps
- Update assertions to match target test case expected results

### 3. Locator Strategy Consistency
- Use the same locator strategies as the template where applicable
- Maintain the template's preference for CSS selectors, XPath, etc.
- Follow the template's element identification patterns

### 4. Best Practices Retention
- Preserve all optimization patterns from the template
- Maintain the template's wait strategies and timeouts
- Keep the template's error handling approaches
- Retain the template's assertion patterns

### 5. Test Data Integration
- Adapt any test data patterns from the template
- Ensure test data is properly integrated into the new script
- Maintain the template's data handling approaches

### 6. Browser Management
- Preserve the template's browser initialization and cleanup
- Maintain the template's page navigation patterns
- Keep the template's browser state management

## Quality Requirements
- The generated script must be syntactically correct Python/PyTest code
- All imports must be properly included
- Function names should follow the pattern: test_{target_tc_id.lower().replace(' ', '_')}_functionality
- Include comprehensive error handling as shown in the template
- Ensure proper browser cleanup and resource management
- Add meaningful assertions for each verification step

## Output Format
Return only the complete Python script code without any additional explanation or markdown formatting.
The script should be ready to execute as a PyTest test case.

## Website Context
{f"Target Website: {website_url}" if website_url else "No specific website URL provided"}

Generate the new test script now, ensuring it follows the template's proven patterns while implementing the target test case requirements.
"""

        logger.info(f"Generated template-based script prompt for {target_tc_id} using template from {template_test_case_id}")
        return prompt

    except Exception as e:
        logger.error(f"Failed to generate template-based script prompt: {e}")
        return _generate_fallback_prompt(target_test_case)


def _format_target_steps(steps: List[Dict[str, Any]]) -> str:
    """
    Format target test case steps for inclusion in the prompt.

    Args:
        steps: List of test case steps

    Returns:
        Formatted string representation of steps
    """
    try:
        if not steps:
            return "- No steps defined"

        formatted_steps = []
        for i, step in enumerate(steps, 1):
            action = step.get('action', 'No action specified')
            expected = step.get('expected_result', 'No expected result specified')

            # Truncate long descriptions
            if len(action) > 100:
                action = action[:97] + "..."
            if len(expected) > 100:
                expected = expected[:97] + "..."

            formatted_steps.append(f"  {i}. **Action**: {action}")
            formatted_steps.append(f"     **Expected**: {expected}")

        return "\n".join(formatted_steps)

    except Exception as e:
        logger.error(f"Failed to format target steps: {e}")
        return "- Error formatting steps"


def _generate_fallback_prompt(target_test_case: Dict[str, Any]) -> str:
    """
    Generate a fallback prompt when template-based generation fails.

    Args:
        target_test_case: The target test case

    Returns:
        Basic prompt for script generation
    """
    try:
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')

        return f"""# Fallback Test Script Generation

## Task
Generate a PyTest automation script for the following test case.

## Test Case Information
- **Test Case ID**: {target_tc_id}
- **Objective**: {target_objective}

## Instructions
Create a basic PyTest script that implements the test case requirements.
Include proper imports, browser setup, and error handling.

Generate the Python script code now.
"""

    except Exception as e:
        logger.error(f"Failed to generate fallback prompt: {e}")
        return "Generate a basic PyTest automation script."


def generate_template_comparison_prompt(
    template_script: Dict[str, Any],
    generated_script: str,
    target_test_case: Dict[str, Any]
) -> str:
    """
    Generate a prompt for comparing template and generated scripts.

    Args:
        template_script: The original template script
        generated_script: The newly generated script
        target_test_case: The target test case

    Returns:
        Prompt for script comparison analysis
    """
    try:
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')

        prompt = f"""# Template vs Generated Script Comparison

## Task
Analyze the generated script against the template to ensure proper adaptation while preserving best practices.

## Template Script (Original - {template_test_case_id})
```python
{template_content}
```

## Generated Script (New - {target_tc_id})
```python
{generated_script}
```

## Analysis Requirements
1. **Structure Preservation**: Verify the generated script maintains the template's overall structure
2. **Pattern Consistency**: Check that optimization patterns from the template are preserved
3. **Content Adaptation**: Confirm the script properly implements the target test case requirements
4. **Quality Assurance**: Identify any syntax errors or missing components

## Output Format
Provide a brief analysis focusing on:
- Structure preservation (Yes/No with brief explanation)
- Pattern consistency (Yes/No with brief explanation)
- Content adaptation quality (Good/Fair/Poor with brief explanation)
- Any critical issues found

Keep the analysis concise and actionable.
"""

        return prompt

    except Exception as e:
        logger.error(f"Failed to generate template comparison prompt: {e}")
        return "Analyze the generated script for quality and consistency."


def enhance_template_prompt_with_context(
    base_prompt: str,
    additional_context: Dict[str, Any]
) -> str:
    """
    Enhance the template-based prompt with additional context including gap analysis.

    Args:
        base_prompt: The base template prompt
        additional_context: Additional context information

    Returns:
        Enhanced prompt with additional context
    """
    try:
        enhancements = []

        # Add browser context if available
        if additional_context.get('browser_type'):
            enhancements.append(f"Target Browser: {additional_context['browser_type']}")

        # Add environment context if available
        if additional_context.get('test_environment'):
            enhancements.append(f"Test Environment: {additional_context['test_environment']}")

        # Add custom instructions if available
        if additional_context.get('custom_instructions'):
            enhancements.append(f"Custom Instructions: {additional_context['custom_instructions']}")

        # Add enhanced gap analysis context if available
        gap_analysis_context = additional_context.get('gap_analysis')
        gap_responses = additional_context.get('gap_responses')
        gap_handling_option = additional_context.get('gap_handling_option')

        if gap_analysis_context:
            gap_section = _build_enhanced_gap_analysis_section(gap_analysis_context, gap_responses, gap_handling_option)
            if gap_section:
                enhancements.append(gap_section)

        # Add failure analysis context if available (for enhanced regeneration)
        failure_analysis = additional_context.get('failure_analysis')
        if failure_analysis:
            failure_section = _build_failure_analysis_enhancement_section(failure_analysis, additional_context)
            if failure_section:
                enhancements.append(failure_section)

        if enhancements:
            enhancement_section = "\n## Additional Context\n" + "\n".join(f"- {enhancement}" for enhancement in enhancements)
            # Insert before the final generation instruction
            insertion_point = base_prompt.rfind("Generate the new test script now")
            if insertion_point != -1:
                enhanced_prompt = (
                    base_prompt[:insertion_point] +
                    enhancement_section + "\n\n" +
                    base_prompt[insertion_point:]
                )
                return enhanced_prompt

        return base_prompt

    except Exception as e:
        logger.error(f"Failed to enhance template prompt with context: {e}")
        return base_prompt


def _build_gap_analysis_enhancement_section(gap_analysis_data: Dict[str, Any], gap_responses: Dict[str, Any]) -> str:
    """
    Build the gap analysis enhancement section for the prompt.

    Args:
        gap_analysis_data: Gap analysis results from AI
        gap_responses: User responses to fill gaps

    Returns:
        str: Gap analysis enhancement section
    """
    try:
        gap_section = "Gap Analysis Results and User Responses:\n"

        # Add overall assessment
        overall_assessment = gap_analysis_data.get('overall_assessment', '')
        if overall_assessment:
            gap_section += f"  - Overall Assessment: {overall_assessment}\n"

        # Add compatibility score
        compatibility_score = gap_analysis_data.get('compatibility_score', '')
        if compatibility_score:
            gap_section += f"  - Compatibility Score: {compatibility_score}%\n"

        # Add identified gaps and user responses
        gaps = gap_analysis_data.get('gaps', [])
        if gaps and gap_responses:
            gap_section += "  - Identified Gaps and User Responses:\n"

            for i, gap in enumerate(gaps):
                gap_key = f"gap_{i}"
                if gap_key in gap_responses:
                    gap_type = gap.get('type', 'general')
                    gap_description = gap.get('description', 'No description')
                    user_response = gap_responses[gap_key].get('response', '')

                    gap_section += f"    * {gap_type.title()}: {gap_description}\n"
                    gap_section += f"      User Response: {user_response}\n"

        # Add additional instructions if provided
        additional_instructions = gap_responses.get('additional_instructions', '')
        if additional_instructions:
            gap_section += f"  - Additional User Instructions: {additional_instructions}\n"

        # Add recommendations
        recommendations = gap_analysis_data.get('recommendations', [])
        if recommendations:
            gap_section += "  - AI Recommendations:\n"
            for rec in recommendations:
                gap_section += f"    * {rec}\n"

        gap_section += "\n  IMPORTANT: Use this gap analysis information to enhance the generated script and address the identified gaps."

        return gap_section

    except Exception as e:
        logger.error(f"Failed to build gap analysis enhancement section: {e}")
        return ""


def _build_enhanced_gap_analysis_section(gap_analysis_data, gap_responses=None, gap_handling_option=None):
    """
    Build enhanced gap analysis section for prompt enhancement with support for different handling options.

    Args:
        gap_analysis_data: Gap analysis results
        gap_responses: User responses to fill gaps (optional)
        gap_handling_option: Selected gap handling option ('targeted', 'inference', or None)

    Returns:
        str: Enhanced gap analysis section for prompt
    """
    try:
        if not gap_analysis_data:
            return None

        gap_section = "Gap Analysis Results:\n"

        # Add compatibility score and assessment
        compatibility_score = gap_analysis_data.get('compatibility_score', 'Unknown')
        overall_assessment = gap_analysis_data.get('overall_assessment', 'No assessment available')

        gap_section += f"  - Compatibility Score: {compatibility_score}%\n"
        gap_section += f"  - Assessment: {overall_assessment}\n"

        # Handle different gap handling options
        gaps = gap_analysis_data.get('gaps', [])
        if gaps:
            if gap_handling_option == "targeted":
                gap_section += _build_targeted_gap_section(gaps, gap_responses)
            elif gap_handling_option == "inference":
                gap_section += _build_inference_gap_section(gaps)
            else:
                # Legacy behavior - use existing function
                legacy_section = _build_gap_analysis_enhancement_section(gap_analysis_data, gap_responses)
                if legacy_section:
                    return legacy_section

        return gap_section

    except Exception as e:
        logger.error(f"Failed to build enhanced gap analysis section: {e}")
        return ""


def _build_targeted_gap_section(gaps, gap_responses):
    """
    Build gap section for targeted gap filling approach.

    Args:
        gaps: List of identified gaps
        gap_responses: User responses to targeted questions

    Returns:
        str: Targeted gap section
    """
    section = "  - Gap Handling: Targeted Gap Filling (User-provided specific information)\n"
    section += "  - Identified Gaps with User Responses:\n"

    for i, gap in enumerate(gaps):
        gap_key = f"gap_{i}"
        gap_type = gap.get('type', 'general')
        gap_description = gap.get('description', 'No description')

        section += f"    * {gap_type.title()}: {gap_description}\n"

        if gap_responses and gap_key in gap_responses:
            user_response = gap_responses[gap_key]
            question = user_response.get('question', 'No question')
            response = user_response.get('response', 'No response')
            section += f"      Question: {question}\n"
            section += f"      User Response: {response}\n"
        else:
            section += f"      Status: No user response provided\n"

    section += "  - Instructions: Use the specific user-provided information to fill gaps precisely. Do not make assumptions.\n"

    return section


def _build_inference_gap_section(gaps):
    """
    Build gap section for intelligent inference approach.

    Args:
        gaps: List of identified gaps

    Returns:
        str: Inference gap section
    """
    section = "  - Gap Handling: Intelligent Inference (AI makes smart assumptions)\n"
    section += "  - Identified Gaps for AI Inference:\n"

    for i, gap in enumerate(gaps):
        gap_type = gap.get('type', 'general')
        gap_description = gap.get('description', 'No description')
        section += f"    * {gap_type.title()}: {gap_description}\n"

    section += "  - Instructions: Make intelligent assumptions for missing data based on context and best practices.\n"
    section += "  - Mark AI-inferred values with comments like '# AI-inferred: [explanation]' for transparency.\n"
    section += "  - Use reasonable defaults and common patterns for missing elements.\n"

    return section


def generate_script_failure_analysis_prompt(
    failed_script_content: str,
    execution_logs: Dict[str, Any],
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    gap_analysis_data: Dict[str, Any] = None,
    gap_responses: Dict[str, Any] = None
) -> str:
    """
    Generate a comprehensive prompt for analyzing script execution failures and suggesting improvements.

    Args:
        failed_script_content: The generated script that failed during execution
        execution_logs: Complete execution results including stdout, stderr, JUnit XML
        template_script: Original template script used for generation
        target_test_case: Target test case information
        gap_analysis_data: Original gap analysis results (optional)
        gap_responses: User responses to gap analysis (optional)

    Returns:
        str: The generated prompt for AI failure analysis
    """
    try:
        # Extract execution information
        stdout = execution_logs.get('stdout', '')
        stderr = execution_logs.get('stderr', '')
        returncode = execution_logs.get('returncode', 'Unknown')
        xml_results = execution_logs.get('xml_results', {})

        # Extract template and test case information
        template_content = template_script.get('content', '')
        template_test_case_id = template_script.get('test_case_id', 'Unknown')
        target_tc_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')
        target_steps = target_test_case.get('Steps', [])

        # Build the failure analysis prompt
        prompt = f"""# Script Execution Failure Analysis & Improvement Recommendations

You are an expert test automation engineer analyzing a failed test script execution. Your task is to identify the root causes of failure and provide specific, actionable recommendations for script improvement.

## Execution Context
- **Template Test Case ID**: {template_test_case_id}
- **Target Test Case ID**: {target_tc_id}
- **Target Objective**: {target_objective}
- **Exit Code**: {returncode}
- **Number of Target Steps**: {len(target_steps)}

## Failed Script Content
```python
{failed_script_content}
```

## Original Template Script (Reference)
```python
{template_content[:1500]}{'...' if len(template_content) > 1500 else ''}
```

## Execution Logs

### Standard Output
```
{stdout[:1000] if stdout else 'No stdout available'}{'...' if len(stdout) > 1000 else ''}
```

### Error Output
```
{stderr[:1000] if stderr else 'No stderr available'}{'...' if len(stderr) > 1000 else ''}
```"""

        # Add JUnit XML results if available
        if xml_results:
            prompt += f"""

### JUnit Test Results
- **Tests Run**: {xml_results.get('tests', 'Unknown')}
- **Failures**: {xml_results.get('failures', 'Unknown')}
- **Errors**: {xml_results.get('errors', 'Unknown')}
- **Skipped**: {xml_results.get('skipped', 'Unknown')}"""

        # Add gap analysis context if available
        if gap_analysis_data:
            gaps = gap_analysis_data.get('gaps', [])
            if gaps:
                prompt += f"""

## Original Gap Analysis
- **Compatibility Score**: {gap_analysis_data.get('compatibility_score', 'Unknown')}
- **Assessment**: {gap_analysis_data.get('overall_assessment', 'No assessment')}
- **Gaps Identified**: {len(gaps)}"""

                for i, gap in enumerate(gaps[:3]):  # Limit to first 3 gaps
                    prompt += f"""
  {i+1}. **{gap.get('type', 'general').title()}**: {gap.get('description', 'No description')}"""

        # Add user gap responses if available
        if gap_responses:
            prompt += f"""

## User Gap Responses
The user provided the following information to address identified gaps:"""
            for key, response_data in list(gap_responses.items())[:3]:  # Limit to first 3 responses
                prompt += f"""
- **{response_data.get('type', 'general').title()}**: {response_data.get('response', 'No response')[:200]}{'...' if len(response_data.get('response', '')) > 200 else ''}"""

        prompt += """

## Analysis Required

Perform a comprehensive failure analysis and provide specific recommendations:

### 1. Root Cause Analysis
- Identify the primary cause(s) of script failure
- Analyze error messages and stack traces
- Compare failed script against working template patterns

### 2. Gap Assessment
- Identify additional gaps not caught in initial analysis
- Assess if original gap analysis was sufficient
- Determine if user responses addressed the right issues

### 3. Script Quality Issues
- Identify coding errors, syntax issues, or logic problems
- Check for missing imports, incorrect selectors, or timing issues
- Assess error handling and browser interaction patterns

### 4. Improvement Recommendations
- Provide specific code fixes and improvements
- Suggest missing elements or better approaches
- Recommend additional error handling or robustness improvements

## Response Format

Return a JSON response with this exact structure:

```json
{
    "failure_analysis": {
        "primary_cause": "<main reason for failure>",
        "secondary_causes": ["<additional contributing factors>"],
        "error_category": "<selenium|syntax|logic|environment|locator|timing>",
        "severity": "<critical|high|medium|low>"
    },
    "gap_assessment": {
        "original_gaps_sufficient": <true/false>,
        "additional_gaps_found": <true/false>,
        "new_gaps": [
            {
                "type": "<gap_type>",
                "description": "<what's missing>",
                "impact": "<how it affects execution>"
            }
        ]
    },
    "script_issues": [
        {
            "issue_type": "<syntax|logic|selector|import|timing>",
            "description": "<specific issue description>",
            "line_reference": "<approximate line or section>",
            "fix_suggestion": "<specific fix recommendation>"
        }
    ],
    "improvement_recommendations": {
        "immediate_fixes": ["<critical fixes needed>"],
        "code_improvements": ["<code quality improvements>"],
        "robustness_enhancements": ["<error handling and reliability improvements>"],
        "regeneration_guidance": "<specific guidance for script regeneration>"
    },
    "regeneration_priority": "<high|medium|low>",
    "confidence_score": <1-10>
}
```

Focus on actionable, specific recommendations that will directly address the execution failure and improve script reliability.
"""

        logger.info(f"Generated script failure analysis prompt for {target_tc_id}")
        return prompt

    except Exception as e:
        logger.error(f"Failed to generate script failure analysis prompt: {e}")
        return _generate_fallback_failure_analysis_prompt(target_test_case)


def _generate_fallback_failure_analysis_prompt(target_test_case: Dict[str, Any]) -> str:
    """
    Generate a basic fallback failure analysis prompt when detailed processing fails.

    Args:
        target_test_case: The test case information

    Returns:
        str: Basic fallback failure analysis prompt
    """
    target_tc_id = target_test_case.get('Test Case ID', 'Unknown')

    return f"""# Basic Script Failure Analysis

Analyze the failed test script execution for Test Case ID: {target_tc_id}

Provide basic recommendations for script improvement in JSON format:

```json
{{
    "failure_analysis": {{
        "primary_cause": "Unable to determine specific cause",
        "error_category": "general",
        "severity": "medium"
    }},
    "improvement_recommendations": {{
        "immediate_fixes": ["Review script syntax and logic"],
        "regeneration_guidance": "Consider regenerating script with additional context"
    }},
    "regeneration_priority": "medium",
    "confidence_score": 5
}}
```
"""


def _build_failure_analysis_enhancement_section(failure_analysis, additional_context):
    """
    Build failure analysis enhancement section for enhanced regeneration prompts.

    Args:
        failure_analysis: Failure analysis results from AI
        additional_context: Additional context including regeneration options

    Returns:
        str: Failure analysis enhancement section
    """
    try:
        regeneration_option = additional_context.get('regeneration_option', 'unknown')
        immediate_fixes = additional_context.get('immediate_fixes', [])
        code_improvements = additional_context.get('code_improvements', [])
        regeneration_guidance = additional_context.get('regeneration_guidance', '')
        additional_gaps = additional_context.get('additional_gaps', [])

        section = f"Failure Analysis Enhanced Regeneration (Mode: {regeneration_option.replace('_', ' ').title()}):\n"

        # Add primary failure cause
        failure_info = failure_analysis.get('failure_analysis', {})
        primary_cause = failure_info.get('primary_cause', 'Unknown cause')
        error_category = failure_info.get('error_category', 'general')
        severity = failure_info.get('severity', 'medium')

        section += f"  - Previous Failure: {primary_cause} (Category: {error_category}, Severity: {severity})\n"

        # Add immediate fixes
        if immediate_fixes:
            section += "  - Critical Fixes Required:\n"
            for fix in immediate_fixes[:3]:  # Limit to 3 most important
                section += f"    * {fix}\n"

        # Add code improvements
        if code_improvements:
            section += "  - Code Quality Improvements:\n"
            for improvement in code_improvements[:3]:  # Limit to 3 most important
                section += f"    * {improvement}\n"

        # Add regeneration guidance
        if regeneration_guidance:
            section += f"  - AI Regeneration Guidance: {regeneration_guidance}\n"

        # Add additional gaps discovered
        if additional_gaps:
            section += "  - Additional Gaps Discovered During Analysis:\n"
            for gap in additional_gaps[:3]:  # Limit to 3 most important
                gap_type = gap.get('type', 'general')
                description = gap.get('description', 'No description')
                section += f"    * {gap_type.title()}: {description}\n"

        # Add regeneration-specific instructions
        if regeneration_option == 'enhanced_targeted':
            section += "  - Instructions: Apply all identified fixes precisely. Use failure analysis insights to improve gap handling.\n"
            section += "  - Focus: Address specific issues identified in the failure analysis with targeted solutions.\n"
        elif regeneration_option == 'enhanced_inference':
            section += "  - Instructions: Apply AI-inferred fixes automatically. Use intelligent assumptions based on failure patterns.\n"
            section += "  - Focus: Implement robust error handling and best practices to prevent similar failures.\n"
        else:
            section += "  - Instructions: Apply failure analysis insights to improve script generation.\n"

        section += "  - Priority: Address all critical fixes before implementing other improvements.\n"
        section += "  - Validation: Ensure the regenerated script addresses the root causes of the previous failure.\n"

        return section

    except Exception as e:
        logger.error(f"Failed to build failure analysis enhancement section: {e}")
        return ""
