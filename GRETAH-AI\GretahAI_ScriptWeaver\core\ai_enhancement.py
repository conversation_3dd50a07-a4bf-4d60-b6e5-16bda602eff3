"""
AI-powered comment and feedback enhancement functionality.

This module handles the enhancement of user comments and feedback using AI,
transforming raw user input into detailed, technical, actionable instructions.
"""

import json
import time
import uuid
from typing import Optional, Dict, Any

# Import GRETAH standardized logging
from debug_utils import debug


def _build_generation_prompts(
    user_comment: str,
    test_case: dict | None,
    step_table_entry: dict | None,
    unified_prompt_context: dict | None = None,
) -> tuple[str, str]:
    system_prompt = """
You are a senior automation architect (Selenium+PyTest, 10 yrs).
Goal: turn raw user feedback + context into a concise to-do list
for regenerating a test script with focus on locator reliability.

Output contract:
• Format: numbered bullets, ≤ 8, no markdown.
• Each bullet ≤ 20 words, starts with an imperative verb (e.g., Use, Improve, Add).
• If a bullet relates to the current step, prefix with [STEP-{step_no}].
• Focus ONLY on: locator strategy improvements, enhanced CSS selectors with ancestor IDs,
  element reliability, wait conditions, and essential automation clarity.
• AVOID: lengthy documentation, verbose descriptions, unrelated commentary.
• End with "#DONE".
• Omit apologies, self-references, or explanations.
"""
    # Build comprehensive context including unified prompt context
    context_blob = {
        "test_case": test_case or {},
        "step": step_table_entry or {},
        "raw_feedback": user_comment,
    }

    # Add unified context information if available
    if unified_prompt_context:
        # Include element data for better locator understanding
        if unified_prompt_context.get('element_data'):
            context_blob["element_data"] = unified_prompt_context['element_data']

        # Include locator resolution data from Stage 4
        if unified_prompt_context.get('locator_resolution_data'):
            context_blob["locator_resolution_data"] = unified_prompt_context['locator_resolution_data']

        # Include session preferences for test data context
        if unified_prompt_context.get('session_preferences'):
            context_blob["session_preferences"] = unified_prompt_context['session_preferences']

        # Include website URL for context
        if unified_prompt_context.get('website_url'):
            context_blob["website_url"] = unified_prompt_context['website_url']

        # Include any existing AI-enhanced context for consistency
        if unified_prompt_context.get('ai_enhanced_context'):
            context_blob["existing_ai_context"] = unified_prompt_context['ai_enhanced_context']

    user_prompt = f"""CONTEXT:
{json.dumps(context_blob, indent=2)}

TASK:
Enhance raw_feedback into actionable bullets focused on locator reliability and automation clarity.
Use the provided element_data and session context to make specific, targeted recommendations.
Return only the bullets.
"""

    return system_prompt.strip(), user_prompt.strip()


def enhance_user_comment_with_ai(
    user_comment: str,
    validation_results: Dict[str, Any],
    *,
    model_name: str = "gemini-2.0-flash",  # Using Gemini 2.0 Flash model
    api_key: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Use an LLM to enhance user's raw comment/feedback into detailed, technical, actionable instructions.

    This function takes a user's raw comment about script optimization issues and enhances it
    with AI to create more detailed, technical, and actionable instructions for script regeneration.

    Args:
        user_comment (str): The user's raw comment/feedback
        validation_results (Dict[str, Any]): Results from script validation
        model_name (str): The AI model to use
        api_key (Optional[str]): API key for Google AI
        context (Optional[Dict[str, Any]]): Additional context information

    Returns:
        str: Enhanced, detailed, and actionable instructions for script improvement

    Raises:
        Exception: If the AI enhancement fails
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    # Generate unique request ID for tracking
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Prepare context for logging
    enhancement_context = {
        'user_comment_length': len(user_comment),
        'has_validation_results': bool(validation_results),
        'validation_status': validation_results.get('validation_status', 'unknown'),
        'model_name': model_name,
        'request_id': request_id
    }

    if context:
        enhancement_context.update(context)

    debug(f"Starting AI comment enhancement [Request ID: {request_id}]",
          stage="ai_enhancement", operation="enhance_user_comment_with_ai",
          context={'request_id': request_id})
    debug(f"User comment length: {len(user_comment)} characters",
          stage="ai_enhancement", operation="enhance_user_comment_with_ai",
          context={'comment_length': len(user_comment)})
    debug(f"Validation results available: {bool(validation_results)}",
          stage="ai_enhancement", operation="enhance_user_comment_with_ai",
          context={'validation_results_available': bool(validation_results)})

    try:
        # Prepare validation context for the prompt
        validation_context = ""
        if validation_results:
            validation_status = validation_results.get('validation_status', 'unknown')
            validation_context = f"\n\nValidation Results:\n- Status: {validation_status}"

            if validation_results.get('issues'):
                validation_context += f"\n- Issues Found: {len(validation_results['issues'])}"
                for i, issue in enumerate(validation_results['issues'][:3], 1):  # Show first 3 issues
                    validation_context += f"\n  {i}. {issue}"

            if validation_results.get('validation_error'):
                validation_context += f"\n- Validation Error: {validation_results['validation_error']}"

        # Create the system prompt for comment enhancement
        system_prompt = """You are a senior QA-automation engineer (10+ yrs, PyTest SME).
Goal: turn raw user feedback + validation data into concise, developer-ready
tasks for regenerating a PyTest script.

Output contract:
• Format: numbered bullet list, max 12 points. No markdown, no chatty text.
• Each point ≤ 25 words, starts with an imperative verb (Fix, Add, Refactor…).
• If an item maps to a validation issue, prefix with [ISSUE-{id}].
• Cover: functional fixes, structure, best-practices, and examples if helpful.
• End with "#DONE".
• Double-check that every point is actionable and PyTest-specific."""

        # Create the user prompt with the comment and validation context
        user_prompt = f"""RAW_FEEDBACK:

\"\"\"{user_comment}\"\"\"
VALIDATION_DATA:
{json.dumps(validation_results, indent=2)}

Transform the feedback into actionable tasks following the contract above.
Return only the bullet list."""

        # Log the prompt generation
        log_ai_interaction(
            function_name="enhance_user_comment_with_ai",
            prompt=user_prompt,
            response="<prompt generation>",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            category="comment_enhancement_prompt",
            is_prompt_generation=True
        )

        # Call the LLM to enhance the comment
        response = generate_llm_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            function_name="enhance_user_comment_with_ai",
            parent_request_id=request_id,
            context=enhancement_context,
            category="comment_enhancement"
        )

        # Clean the response to remove any markdown formatting
        from .ai_helpers import clean_llm_response
        enhanced_comment = clean_llm_response(response, "text")

        # Calculate total time
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the successful enhancement
        log_ai_interaction(
            function_name="enhance_user_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response=f"Enhanced comment length: {len(enhanced_comment)} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **enhancement_context,
                'original_comment_length': len(user_comment),
                'enhanced_comment_length': len(enhanced_comment),
                'total_processing_time_ms': total_time_ms
            },
            latency_ms=total_time_ms,
            category="comment_enhancement_success"
        )

        debug(f"AI comment enhancement completed successfully [Request ID: {request_id}]",
              stage="ai_enhancement", operation="enhance_user_comment_with_ai",
              context={'request_id': request_id, 'success': True})
        debug(f"Enhanced comment length: {len(enhanced_comment)} characters",
              stage="ai_enhancement", operation="enhance_user_comment_with_ai",
              context={'enhanced_comment_length': len(enhanced_comment)})
        debug(f"Processing time: {total_time_ms:.2f}ms",
              stage="ai_enhancement", operation="enhance_user_comment_with_ai",
              context={'processing_time_ms': total_time_ms})

        return enhanced_comment

    except Exception as e:
        debug(f"Error in enhance_user_comment_with_ai: {e}",
              stage="ai_enhancement", operation="enhance_user_comment_with_ai",
              context={'error': str(e), 'error_type': type(e).__name__})
        debug(f"Error details: {str(e)}",
              stage="ai_enhancement", operation="enhance_user_comment_with_ai",
              context={'error_details': str(e)})

        # Log the error
        log_ai_interaction(
            function_name="enhance_user_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response="",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            error=str(e),
            category="comment_enhancement_error"
        )

        # Return the original comment as fallback
        debug("Returning original comment due to enhancement error",
              stage="ai_enhancement", operation="enhance_user_comment_with_ai",
              context={'fallback': True})
        return user_comment


def enhance_generation_comment_with_ai(
    user_comment: str,
    test_case: Dict[str, Any],
    step_table_entry: Optional[Dict[str, Any]] = None,
    *,
    model_name: str = "gemini-2.0-flash",  # Using Gemini 2.0 Flash model
    api_key: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    unified_prompt_context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Use an LLM to enhance user's raw comment/feedback into detailed, technical, actionable instructions for script generation.

    This function takes a user's raw comment about script generation issues and enhances it
    with AI to create more detailed, technical, and actionable instructions for script regeneration.

    The function now supports unified prompt context to ensure consistency with script generation workflows.

    Args:
        user_comment (str): The user's raw comment/feedback about script generation
        test_case (Dict[str, Any]): The test case information for context
        step_table_entry (Optional[Dict[str, Any]]): The step table entry for additional context
        model_name (str): The AI model to use for enhancement (default: "gemini-2.0-flash")
        api_key (Optional[str]): API key for Google AI. If None, use initialized client
        context (Optional[Dict[str, Any]]): Additional context for logging
        unified_prompt_context (Optional[Dict[str, Any]]): Unified prompt context containing all
            context sources (element data, session preferences, AI-enhanced context, etc.)

    Returns:
        str: Enhanced, detailed, technical instructions for script generation

    Raises:
        Exception: If the AI enhancement fails
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    # Generate unique request ID for tracking
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Prepare context for logging
    enhancement_context = {
        'user_comment_length': len(user_comment),
        'has_test_case': bool(test_case),
        'has_step_table_entry': bool(step_table_entry),
        'test_case_id': test_case.get('Test Case ID', 'unknown') if test_case else 'unknown',
        'step_no': step_table_entry.get('step_no', 'unknown') if step_table_entry else 'unknown',
        'model_name': model_name,
        'request_id': request_id
    }

    if context:
        enhancement_context.update(context)

    try:
        debug(f"Starting generation comment enhancement [Request ID: {request_id}]",
              stage="ai_enhancement", operation="enhance_generation_comment_with_ai",
              context={'request_id': request_id})
        debug(f"User comment length: {len(user_comment)} characters",
              stage="ai_enhancement", operation="enhance_generation_comment_with_ai",
              context={'comment_length': len(user_comment)})

        # Create system prompt for generation comment enhancement with unified context
        system_prompt, user_prompt = _build_generation_prompts(
            user_comment, test_case, step_table_entry, unified_prompt_context
        )

        # Log the prompt generation
        log_ai_interaction(
            function_name="enhance_generation_comment_with_ai",
            prompt=user_prompt,
            response="<prompt generation>",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            category="generation_comment_enhancement_prompt",
            is_prompt_generation=True
        )

        # Call the LLM to enhance the comment
        response = generate_llm_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            function_name="enhance_generation_comment_with_ai",
            parent_request_id=request_id,
            context=enhancement_context,
            category="generation_comment_enhancement"
        )

        # Clean the response to remove any markdown formatting
        from .ai_helpers import clean_llm_response
        enhanced_comment = clean_llm_response(response, "text")

        # Calculate total time
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the successful enhancement
        log_ai_interaction(
            function_name="enhance_generation_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response=f"Enhanced comment length: {len(enhanced_comment)} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **enhancement_context,
                'original_comment_length': len(user_comment),
                'enhanced_comment_length': len(enhanced_comment),
                'total_processing_time_ms': total_time_ms
            },
            latency_ms=total_time_ms,
            category="generation_comment_enhancement_success"
        )

        debug(f"Generation comment enhancement completed successfully [Request ID: {request_id}]",
              stage="ai_enhancement", operation="enhance_generation_comment_with_ai",
              context={'request_id': request_id, 'success': True})
        return enhanced_comment

    except Exception as e:
        debug(f"Error in enhance_generation_comment_with_ai: {e}",
              stage="ai_enhancement", operation="enhance_generation_comment_with_ai",
              context={'error': str(e), 'error_type': type(e).__name__})
        debug(f"Error details: {str(e)}",
              stage="ai_enhancement", operation="enhance_generation_comment_with_ai",
              context={'error_details': str(e)})

        # Log the error
        log_ai_interaction(
            function_name="enhance_generation_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response="",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            error=str(e),
            category="generation_comment_enhancement_error"
        )

        # Return the original comment as fallback
        debug("Returning original comment due to enhancement error",
              stage="ai_enhancement", operation="enhance_generation_comment_with_ai",
              context={'fallback': True})
        return user_comment
