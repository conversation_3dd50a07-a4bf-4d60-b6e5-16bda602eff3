"""
Export utilities for the GretahAI CaseForge database system.

This module handles exporting test cases to various formats including Excel,
and provides export functionality for reporting and data sharing.
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock

# Use absolute imports instead of relative imports beyond package
try:
    from helpers.csv.export import export_test_cases_to_csv as csv_export_helper
except ImportError:
    # Fallback if helpers package not available
    csv_export_helper = None


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def export_test_cases_to_excel(database_path, jira_id, test_type, output_file):
    """
    Exports test cases from the database to an Excel file.
    
    This function retrieves test cases for a specific JIRA ID and test type,
    formats them appropriately, and exports them to an Excel file with proper
    formatting and structure for sharing and reporting purposes.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        test_type (str): Test type filter ("positive", "negative", "security", "performance", "all")
        output_file (str): Path where the Excel file should be saved

    Returns:
        bool: True if export successful, False otherwise

    Process Flow:
        1. Establishes database connection
        2. Queries test cases and test steps for the specified criteria
        3. Formats data into a structured DataFrame
        4. Applies Excel-specific formatting and styling
        5. Saves the file to the specified location
        6. Handles any export errors gracefully

    Data Structure:
        The exported Excel file contains columns for:
        - Test Case ID, Objective, Prerequisites
        - Test Steps with step numbers
        - Expected Results, Actual Results
        - Test Status, Priority, Type information
        - Timestamps and user information

    Example:
        success = export_test_cases_to_excel(
            db_path, "TP-1", "positive", "output/test_cases.xlsx"
        )
        if success:
            print("Export completed successfully")
    """
    conn = None
    try:
        # Connect to the database
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Query to get test cases and their steps
        if test_type.lower() == "all":
            query = """
                SELECT 
                    tc.test_case_id as "Test Case ID",
                    tc.test_case_objective as "Test Case Objective",
                    tc.prerequisite as "Prerequisite",
                    tc.priority as "Priority",
                    tc.test_type as "Test Type",
                    tc.test_group as "Test Group",
                    tc.project as "Project",
                    tc.feature as "Feature",
                    tc.dashboard_test_type as "Dashboard Test Type",
                    tc.user_name as "User Name",
                    tc.timestamp as "Timestamp",
                    tc.is_edited as "Is Edited",
                    ts.step_number as "Step No",
                    ts.test_step as "Test Steps",
                    ts.expected_result as "Expected Result",
                    ts.actual_result as "Actual Result",
                    ts.test_status as "Test Status",
                    ts.defect_id as "Defect ID",
                    ts.comments as "Comments"
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ?
                ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (jira_id,))
        else:
            query = """
                SELECT 
                    tc.test_case_id as "Test Case ID",
                    tc.test_case_objective as "Test Case Objective",
                    tc.prerequisite as "Prerequisite",
                    tc.priority as "Priority",
                    tc.test_type as "Test Type",
                    tc.test_group as "Test Group",
                    tc.project as "Project",
                    tc.feature as "Feature",
                    tc.dashboard_test_type as "Dashboard Test Type",
                    tc.user_name as "User Name",
                    tc.timestamp as "Timestamp",
                    tc.is_edited as "Is Edited",
                    ts.step_number as "Step No",
                    ts.test_step as "Test Steps",
                    ts.expected_result as "Expected Result",
                    ts.actual_result as "Actual Result",
                    ts.test_status as "Test Status",
                    ts.defect_id as "Defect ID",
                    ts.comments as "Comments"
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
                ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (jira_id, test_type.lower()))

        results = cursor.fetchall()
        
        if not results:
            print(f"No test cases found for {jira_id} with test type {test_type}")
            return False

        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        # Ensure the output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Export to Excel with better error handling
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Test Cases', index=False)
                
                # Get the workbook and worksheet for formatting
                workbook = writer.book
                worksheet = writer.sheets['Test Cases']
                
                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # Verify file was created successfully
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                print(f"Successfully exported {len(df)} rows to {output_file}")
                return True
            else:
                print(f"Failed to create Excel file: {output_file}")
                return False
                
        except Exception as excel_error:
            print(f"Error writing Excel file: {excel_error}")
            return False

    except Exception as e:
        print(f"Error exporting test cases to Excel: {e}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def create_backup_filename(database_path):
    """
    Creates a backup filename with timestamp.
    
    Args:
        database_path (str): Original database path
        
    Returns:
        str: Backup filename with timestamp
    """
    directory = os.path.dirname(database_path)
    base_name = os.path.splitext(os.path.basename(database_path))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"{base_name}_backup_{timestamp}.db"
    return os.path.join(directory, "backups", backup_filename)


def export_test_cases_to_csv(database_path, jira_id, test_type, output_file):
    """
    Exports test cases to CSV format.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier
        test_type (str): Test type filter
        output_file (str): Path where the CSV file should be saved

    Returns:
        bool: True if export successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Query test cases
        if test_type.lower() == "all":
            query = """
                SELECT 
                    tc.test_case_id, tc.test_case_objective, tc.prerequisite,
                    tc.priority, tc.test_type, tc.test_group, tc.project, tc.feature,
                    tc.dashboard_test_type, tc.user_name, tc.timestamp, tc.is_edited,
                    ts.step_number, ts.test_step, ts.expected_result, ts.actual_result,
                    ts.test_status, ts.defect_id, ts.comments
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ?
                ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (jira_id,))
        else:
            query = """
                SELECT 
                    tc.test_case_id, tc.test_case_objective, tc.prerequisite,
                    tc.priority, tc.test_type, tc.test_group, tc.project, tc.feature,
                    tc.dashboard_test_type, tc.user_name, tc.timestamp, tc.is_edited,
                    ts.step_number, ts.test_step, ts.expected_result, ts.actual_result,
                    ts.test_status, ts.defect_id, ts.comments
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
                ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (jira_id, test_type.lower()))

        results = cursor.fetchall()
        
        if not results:
            print(f"No test cases found for {jira_id} with test type {test_type}")
            return False

        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        # Ensure the output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Export to CSV
        df.to_csv(output_file, index=False)
        
        print(f"Successfully exported {len(df)} rows to {output_file}")
        return True

    except Exception as e:
        print(f"Error exporting test cases to CSV: {e}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def export_test_cases_to_csv_enhanced(database_path, jira_id, test_type, output_file):
    """
    Enhanced CSV export with better error handling using the CSV helper.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier
        test_type (str): Test type filter
        output_file (str): Path where the CSV file should be saved

    Returns:
        bool: True if export successful, False otherwise
    """
    # Get data from database first
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Query test cases (same as existing export_test_cases_to_csv)
        if test_type.lower() == "all":
            query = """
                SELECT 
                    tc.test_case_id, tc.test_case_objective, tc.prerequisite,
                    tc.priority, tc.test_type, tc.test_group, tc.project, tc.feature,
                    tc.dashboard_test_type, tc.user_name, tc.timestamp, tc.is_edited,
                    ts.step_number, ts.test_step, ts.expected_result, ts.actual_result,
                    ts.test_status, ts.defect_id, ts.comments
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ?
                ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (jira_id,))
        else:
            query = """
                SELECT 
                    tc.test_case_id, tc.test_case_objective, tc.prerequisite,
                    tc.priority, tc.test_type, tc.test_group, tc.project, tc.feature,
                    tc.dashboard_test_type, tc.user_name, tc.timestamp, tc.is_edited,
                    ts.step_number, ts.test_step, ts.expected_result, ts.actual_result,
                    ts.test_status, ts.defect_id, ts.comments
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
                ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (jira_id, test_type.lower()))

        results = cursor.fetchall()
        
        if not results:
            print(f"No test cases found for {jira_id} with test type {test_type}")
            return False

        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        # Use the CSV helper function for formatting and export
        success, actual_path, error_msg = csv_export_helper(df, jira_id, test_type, os.path.dirname(output_file))
        
        if success:
            # Move file to requested location if different
            if actual_path != output_file:
                import shutil
                shutil.move(actual_path, output_file)
            return True
        else:
            print(f"CSV export failed: {error_msg}")
            return False

    except Exception as e:
        print(f"Error exporting test cases to CSV: {e}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def export_summary_report(database_path, jira_id, output_file):
    """
    Exports a summary report of test cases for a JIRA ID.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier
        output_file (str): Path where the report should be saved

    Returns:
        bool: True if export successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Get summary statistics
        cursor.execute("""
            SELECT 
                dashboard_test_type,
                COUNT(DISTINCT test_case_id) as test_case_count,
                COUNT(ts.id) as total_steps,
                user_name,
                MAX(timestamp) as latest_update
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.jira_id = ?
            GROUP BY dashboard_test_type, user_name
        """, (jira_id,))
        
        summary_data = cursor.fetchall()
        
        if not summary_data:
            print(f"No data found for {jira_id}")
            return False
            
        # Create summary DataFrame
        summary_df = pd.DataFrame([dict(row) for row in summary_data])
        
        # Ensure the output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Export summary to Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
        print(f"Successfully exported summary report to {output_file}")
        return True
        
    except Exception as e:
        print(f"Error exporting summary report: {e}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")
